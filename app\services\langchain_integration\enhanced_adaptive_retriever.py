"""
Enhanced LangChain AdaptiveRetriever

Enhanced version of the LangChain AdaptiveRetriever that provides comprehensive
multimodal content retrieval with rich context assembly, including images,
tables, and spatial relationships for better LLM context.
"""

import logging
from typing import List, Dict, Any, Optional

from langchain_core.retrievers import BaseRetriever
from langchain_core.documents import Document
from langchain_core.callbacks import CallbackManagerForRetrieverRun

from app.services.langchain_integration.adaptive_retriever import AdaptiveRetriever
from app.services.enhanced_multimodal_context_service import get_enhanced_multimodal_context_service
from app.services.multimodal_query_analyzer import get_multimodal_query_analyzer
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class EnhancedAdaptiveRetriever(AdaptiveRetriever):
    """
    Enhanced LangChain retriever with comprehensive multimodal capabilities.
    
    This retriever extends the base AdaptiveRetriever to provide:
    - Advanced multimodal query analysis
    - Rich context assembly with images and tables
    - Spatial relationship preservation
    - Enhanced content prioritization
    - Comprehensive source attribution
    """
    
    def __init__(
        self,
        vectorstore,
        base_k: int = 12,
        category: Optional[str] = None,
        enable_enhanced_context: bool = True,
        include_spatial_relationships: bool = True,
        include_cross_references: bool = True,
        max_context_length: int = 6000,
        **kwargs
    ):
        """
        Initialize the enhanced adaptive retriever.
        
        Args:
            vectorstore: ChromaDB vector store wrapper
            base_k: Base number of documents to retrieve
            category: Default category for retrieval
            enable_enhanced_context: Whether to use enhanced context assembly
            include_spatial_relationships: Whether to include spatial context
            include_cross_references: Whether to include cross-references
            max_context_length: Maximum length for text content
            **kwargs: Additional arguments passed to AdaptiveRetriever
        """
        super().__init__(
            vectorstore=vectorstore,
            base_k=base_k,
            category=category,
            enable_multimodal=True,  # Always enable multimodal for enhanced retriever
            **kwargs
        )
        
        self.enable_enhanced_context = enable_enhanced_context
        self.include_spatial_relationships = include_spatial_relationships
        self.include_cross_references = include_cross_references
        self.max_context_length = max_context_length
        
        # Initialize enhanced services
        if self.enable_enhanced_context:
            self.context_service = get_enhanced_multimodal_context_service()
            self.query_analyzer = get_multimodal_query_analyzer()
        
        logger.info(f"Initialized EnhancedAdaptiveRetriever with enhanced_context={enable_enhanced_context}")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def _get_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun,
        **kwargs
    ) -> List[Document]:
        """
        Retrieve relevant documents with enhanced multimodal context assembly.
        
        Args:
            query: Search query
            run_manager: Callback manager for the retriever run
            **kwargs: Additional search parameters
            
        Returns:
            List of documents with enhanced multimodal context
        """
        try:
            logger.debug(f"Enhanced retrieval for query: {query[:50]}...")
            
            # Get base documents using parent class
            base_documents = super()._get_relevant_documents(query, run_manager=run_manager, **kwargs)
            
            if not self.enable_enhanced_context:
                return base_documents
            
            # Assemble enhanced multimodal context
            enhanced_context = self.context_service.assemble_enhanced_context(
                query=query,
                retrieved_documents=base_documents,
                max_context_length=self.max_context_length,
                include_spatial_relationships=self.include_spatial_relationships,
                include_cross_references=self.include_cross_references,
                prioritize_by_query_intent=True
            )
            
            # Create enhanced documents with rich context
            enhanced_documents = self._create_enhanced_documents(enhanced_context, base_documents)
            
            logger.info(f"Enhanced retrieval completed: {len(enhanced_documents)} documents with rich context")
            return enhanced_documents
            
        except Exception as e:
            logger.error(f"Error in enhanced retrieval: {e}")
            # Fallback to base retrieval
            return super()._get_relevant_documents(query, run_manager=run_manager, **kwargs)
    
    def _create_enhanced_documents(
        self,
        enhanced_context,
        base_documents: List[Document]
    ) -> List[Document]:
        """
        Create enhanced documents with rich multimodal context.
        
        Args:
            enhanced_context: Enhanced multimodal context from context service
            base_documents: Original retrieved documents
            
        Returns:
            List of enhanced documents with multimodal context
        """
        enhanced_documents = []
        
        try:
            # Create a comprehensive context document
            context_doc = Document(
                page_content=enhanced_context.formatted_context,
                metadata={
                    "content_type": "enhanced_multimodal_context",
                    "query": enhanced_context.query,
                    "query_type": enhanced_context.query_analysis.query_type.value,
                    "query_confidence": enhanced_context.query_analysis.confidence_score,
                    "context_metadata": enhanced_context.context_metadata,
                    "source_attribution": enhanced_context.source_attribution,
                    "total_images": enhanced_context.image_content.get("total_images", 0),
                    "total_tables": enhanced_context.table_content.get("total_tables", 0),
                    "total_multimodal_chunks": len(enhanced_context.multimodal_chunks),
                    "has_spatial_relationships": bool(enhanced_context.spatial_relationships),
                    "enhanced_retrieval": True,
                    "context_richness_score": enhanced_context.context_metadata.get("context_richness_score", 0)
                }
            )
            enhanced_documents.append(context_doc)
            
            # Add individual content documents with enhanced metadata
            self._add_enhanced_text_documents(enhanced_documents, enhanced_context)
            self._add_enhanced_image_documents(enhanced_documents, enhanced_context)
            self._add_enhanced_table_documents(enhanced_documents, enhanced_context)
            self._add_enhanced_multimodal_documents(enhanced_documents, enhanced_context)
            
            # Add original documents with enhanced metadata
            for doc in base_documents[:5]:  # Limit to top 5 original documents
                enhanced_doc = Document(
                    page_content=doc.page_content,
                    metadata={
                        **doc.metadata,
                        "enhanced_retrieval": True,
                        "original_document": True,
                        "query_relevance": self._calculate_enhanced_relevance(doc, enhanced_context)
                    }
                )
                enhanced_documents.append(enhanced_doc)
            
            return enhanced_documents
            
        except Exception as e:
            logger.error(f"Error creating enhanced documents: {e}")
            return base_documents
    
    def _add_enhanced_text_documents(self, enhanced_documents: List[Document], enhanced_context) -> None:
        """Add enhanced text documents to the result."""
        text_chunks = enhanced_context.text_content.get("chunks", [])
        
        for i, chunk in enumerate(text_chunks[:3]):  # Top 3 text chunks
            if chunk.get("content"):
                doc = Document(
                    page_content=chunk["content"],
                    metadata={
                        "content_type": "enhanced_text_chunk",
                        "chunk_index": i,
                        "source": chunk.get("source", "unknown"),
                        "page_num": chunk.get("page_num"),
                        "relevance_score": chunk.get("relevance_score", 0),
                        "query_relevance": chunk.get("query_relevance", 0),
                        "enhanced_retrieval": True
                    }
                )
                enhanced_documents.append(doc)
    
    def _add_enhanced_image_documents(self, enhanced_documents: List[Document], enhanced_context) -> None:
        """Add enhanced image documents to the result."""
        images = enhanced_context.image_content.get("images", [])
        
        for i, image in enumerate(images[:3]):  # Top 3 images
            # Create document for image description
            image_content_parts = []
            
            if image.get("caption"):
                image_content_parts.append(f"Caption: {image['caption']}")
            
            if image.get("description"):
                image_content_parts.append(f"Description: {image['description']}")
            
            vision_analysis = image.get("vision_analysis", {})
            if vision_analysis.get("description"):
                image_content_parts.append(f"Visual Analysis: {vision_analysis['description']}")
            
            if image_content_parts:
                doc = Document(
                    page_content="\n".join(image_content_parts),
                    metadata={
                        "content_type": "enhanced_image_content",
                        "image_index": i,
                        "content_hash": image.get("content_hash"),
                        "page_num": image.get("metadata", {}).get("page_num"),
                        "image_format": image.get("metadata", {}).get("format"),
                        "image_dimensions": image.get("metadata", {}).get("dimensions"),
                        "relevance_score": image.get("metadata", {}).get("relevance_score", 0),
                        "query_relevance": image.get("metadata", {}).get("query_relevance", 0),
                        "spatial_context": image.get("spatial_context", {}),
                        "enhanced_retrieval": True
                    }
                )
                enhanced_documents.append(doc)
    
    def _add_enhanced_table_documents(self, enhanced_documents: List[Document], enhanced_context) -> None:
        """Add enhanced table documents to the result."""
        tables = enhanced_context.table_content.get("tables", [])
        
        for i, table in enumerate(tables[:3]):  # Top 3 tables
            # Create document for table content
            table_content_parts = []
            
            if table.get("context"):
                table_content_parts.append(f"Context: {table['context']}")
            
            if table.get("description"):
                table_content_parts.append(f"Description: {table['description']}")
            
            if table.get("markdown"):
                table_content_parts.append(f"Table Data:\n{table['markdown']}")
            
            metadata = table.get("metadata", {})
            if metadata.get("num_rows") and metadata.get("num_cols"):
                table_content_parts.append(f"Table Size: {metadata['num_rows']} rows × {metadata['num_cols']} columns")
            
            if table_content_parts:
                doc = Document(
                    page_content="\n".join(table_content_parts),
                    metadata={
                        "content_type": "enhanced_table_content",
                        "table_index": i,
                        "content_hash": table.get("content_hash"),
                        "page_num": metadata.get("page_num"),
                        "num_rows": metadata.get("num_rows"),
                        "num_cols": metadata.get("num_cols"),
                        "extraction_method": metadata.get("extraction_method"),
                        "relevance_score": metadata.get("relevance_score", 0),
                        "query_relevance": metadata.get("query_relevance", 0),
                        "spatial_context": table.get("spatial_context", {}),
                        "structured_data": table.get("structured_data", {}),
                        "enhanced_retrieval": True
                    }
                )
                enhanced_documents.append(doc)
    
    def _add_enhanced_multimodal_documents(self, enhanced_documents: List[Document], enhanced_context) -> None:
        """Add enhanced multimodal chunk documents to the result."""
        multimodal_chunks = enhanced_context.multimodal_chunks
        
        for i, chunk in enumerate(multimodal_chunks[:2]):  # Top 2 multimodal chunks
            # Enhance multimodal chunk content
            enhanced_content_parts = [chunk["content"]]
            
            if chunk.get("num_images", 0) > 0:
                enhanced_content_parts.append(f"\n[This section contains {chunk['num_images']} image(s)]")
            
            if chunk.get("num_tables", 0) > 0:
                enhanced_content_parts.append(f"\n[This section contains {chunk['num_tables']} table(s)]")
            
            spatial_relationships = chunk.get("spatial_relationships", {})
            if spatial_relationships:
                enhanced_content_parts.append(f"\n[Spatial context available]")
            
            doc = Document(
                page_content="".join(enhanced_content_parts),
                metadata={
                    "content_type": "enhanced_multimodal_chunk",
                    "chunk_index": i,
                    "page_num": chunk.get("page_num"),
                    "chunk_type": chunk.get("chunk_type"),
                    "content_types": chunk.get("content_types", []),
                    "num_images": chunk.get("num_images", 0),
                    "num_tables": chunk.get("num_tables", 0),
                    "relevance_score": chunk.get("relevance_score", 0),
                    "query_relevance": chunk.get("query_relevance", 0),
                    "multimodal_priority": chunk.get("multimodal_priority", False),
                    "spatial_relationships": spatial_relationships,
                    "enhanced_retrieval": True
                }
            )
            enhanced_documents.append(doc)
    
    def _calculate_enhanced_relevance(self, doc: Document, enhanced_context) -> float:
        """Calculate enhanced relevance score based on query analysis."""
        base_score = doc.metadata.get("relevance_score", 0)
        content_type = doc.metadata.get("content_type", "text")
        
        # Apply query-specific weights
        content_weights = enhanced_context.query_analysis.content_type_weights
        
        if content_type in ["image_caption", "image_analysis"]:
            weight = content_weights.get("image", 1.0)
        elif content_type in ["table_markdown", "table_context"]:
            weight = content_weights.get("table", 1.0)
        elif content_type == "multimodal_chunk":
            weight = content_weights.get("multimodal_chunk", 1.0)
        else:
            weight = content_weights.get("text", 1.0)
        
        return base_score * weight
    
    def get_enhanced_retriever_info(self) -> Dict[str, Any]:
        """Get information about the enhanced retriever configuration."""
        base_info = self.get_retriever_info()
        
        enhanced_info = {
            **base_info,
            "retriever_type": "enhanced_adaptive",
            "enhanced_context_enabled": self.enable_enhanced_context,
            "spatial_relationships_enabled": self.include_spatial_relationships,
            "cross_references_enabled": self.include_cross_references,
            "max_context_length": self.max_context_length,
            "services": {
                "context_service": self.context_service is not None,
                "query_analyzer": self.query_analyzer is not None
            }
        }
        
        return enhanced_info
    
    def update_enhanced_config(
        self,
        enable_enhanced_context: Optional[bool] = None,
        include_spatial_relationships: Optional[bool] = None,
        include_cross_references: Optional[bool] = None,
        max_context_length: Optional[int] = None,
        **kwargs
    ):
        """Update enhanced retriever configuration."""
        if enable_enhanced_context is not None:
            self.enable_enhanced_context = enable_enhanced_context
        if include_spatial_relationships is not None:
            self.include_spatial_relationships = include_spatial_relationships
        if include_cross_references is not None:
            self.include_cross_references = include_cross_references
        if max_context_length is not None:
            self.max_context_length = max_context_length
        
        # Update base configuration
        self.update_config(**kwargs)
        
        logger.info(f"Updated enhanced retriever config: enhanced_context={self.enable_enhanced_context}")


def get_enhanced_adaptive_retriever(
    vectorstore,
    base_k: int = 12,
    category: Optional[str] = None,
    enable_enhanced_context: bool = True,
    **kwargs
) -> EnhancedAdaptiveRetriever:
    """
    Factory function to create an enhanced adaptive retriever.
    
    Args:
        vectorstore: ChromaDB vector store wrapper
        base_k: Base number of documents to retrieve
        category: Default category for retrieval
        enable_enhanced_context: Whether to use enhanced context assembly
        **kwargs: Additional arguments
        
    Returns:
        EnhancedAdaptiveRetriever instance
    """
    return EnhancedAdaptiveRetriever(
        vectorstore=vectorstore,
        base_k=base_k,
        category=category,
        enable_enhanced_context=enable_enhanced_context,
        **kwargs
    )

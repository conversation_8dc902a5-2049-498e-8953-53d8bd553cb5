"""
Enhanced Multimodal Context Service

Central service for assembling rich multimodal context that combines text, images,
and tables with spatial relationships and proper source attribution. This service
provides comprehensive context assembly for both LangChain and LlamaIndex frameworks.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from langchain_core.documents import Document

from app.services.multimodal_query_analyzer import get_multimodal_query_analyzer, MultimodalQueryAnalysis
from app.services.context_assembler import get_context_assembler
from app.services.multimodal_storage import get_multimodal_storage
from app.utils.performance_monitor import performance_monitor
from config.multimodal_config import ContentType

logger = logging.getLogger(__name__)


@dataclass
class EnhancedMultimodalContext:
    """Enhanced multimodal context with rich content and relationships."""
    query: str
    query_analysis: MultimodalQueryAnalysis
    
    # Content sections
    text_content: Dict[str, Any]
    image_content: Dict[str, Any]
    table_content: Dict[str, Any]
    multimodal_chunks: List[Dict[str, Any]]
    
    # Relationships and spatial context
    spatial_relationships: Dict[str, Any]
    content_relationships: List[Dict[str, Any]]
    cross_references: Dict[str, List[str]]
    
    # Context metadata
    context_metadata: Dict[str, Any]
    source_attribution: Dict[str, Any]
    
    # Formatted context for LLM
    formatted_context: str
    structured_context: Dict[str, Any]


class EnhancedMultimodalContextService:
    """
    Enhanced service for assembling comprehensive multimodal context.
    
    This service provides sophisticated context assembly that goes beyond
    the basic context assembler to include rich spatial relationships,
    content prioritization, and enhanced formatting for LLM consumption.
    """
    
    def __init__(self):
        """Initialize the enhanced multimodal context service."""
        self.query_analyzer = get_multimodal_query_analyzer()
        self.context_assembler = get_context_assembler()
        self.storage = get_multimodal_storage()
        
        logger.info("Initialized EnhancedMultimodalContextService")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def assemble_enhanced_context(
        self,
        query: str,
        retrieved_documents: List[Document],
        max_context_length: int = 6000,
        include_spatial_relationships: bool = True,
        include_cross_references: bool = True,
        prioritize_by_query_intent: bool = True
    ) -> EnhancedMultimodalContext:
        """
        Assemble enhanced multimodal context from retrieved documents.
        
        Args:
            query: Original user query
            retrieved_documents: Documents retrieved from vector store
            max_context_length: Maximum length for text content
            include_spatial_relationships: Whether to include spatial context
            include_cross_references: Whether to include cross-references
            prioritize_by_query_intent: Whether to prioritize by query analysis
            
        Returns:
            EnhancedMultimodalContext with comprehensive context
        """
        try:
            logger.info(f"Assembling enhanced multimodal context for query: {query[:50]}...")
            
            # Analyze query for multimodal intent
            query_analysis = self.query_analyzer.analyze_query(query)
            
            # Group and prioritize documents
            grouped_docs = self._group_and_prioritize_documents(
                retrieved_documents, query_analysis, prioritize_by_query_intent
            )
            
            # Assemble content sections
            text_content = self._assemble_enhanced_text_content(
                grouped_docs.get("text", []), max_context_length, query_analysis
            )
            
            image_content = self._assemble_enhanced_image_content(
                grouped_docs.get("image", []), query_analysis
            )
            
            table_content = self._assemble_enhanced_table_content(
                grouped_docs.get("table", []), query_analysis
            )
            
            multimodal_chunks = self._assemble_multimodal_chunks(
                grouped_docs.get("multimodal", []), query_analysis
            )
            
            # Build relationships and spatial context
            spatial_relationships = {}
            content_relationships = []
            cross_references = {}
            
            if include_spatial_relationships:
                spatial_relationships = self._build_spatial_relationships(retrieved_documents)
            
            if include_cross_references:
                content_relationships = self._build_content_relationships(retrieved_documents)
                cross_references = self._build_cross_references(retrieved_documents)
            
            # Create context metadata
            context_metadata = self._create_enhanced_metadata(
                query_analysis, text_content, image_content, table_content, 
                multimodal_chunks, spatial_relationships
            )
            
            # Create source attribution
            source_attribution = self._create_source_attribution(retrieved_documents)
            
            # Format context for LLM
            formatted_context = self._format_enhanced_context_for_llm(
                query, query_analysis, text_content, image_content, 
                table_content, multimodal_chunks, spatial_relationships
            )
            
            # Create structured context
            structured_context = self._create_structured_context(
                text_content, image_content, table_content, multimodal_chunks
            )
            
            # Create enhanced context object
            enhanced_context = EnhancedMultimodalContext(
                query=query,
                query_analysis=query_analysis,
                text_content=text_content,
                image_content=image_content,
                table_content=table_content,
                multimodal_chunks=multimodal_chunks,
                spatial_relationships=spatial_relationships,
                content_relationships=content_relationships,
                cross_references=cross_references,
                context_metadata=context_metadata,
                source_attribution=source_attribution,
                formatted_context=formatted_context,
                structured_context=structured_context
            )
            
            logger.info(f"Enhanced context assembled with {len(retrieved_documents)} documents")
            return enhanced_context
            
        except Exception as e:
            logger.error(f"Error assembling enhanced context: {e}")
            return self._create_fallback_context(query, retrieved_documents)
    
    def _group_and_prioritize_documents(
        self,
        documents: List[Document],
        query_analysis: MultimodalQueryAnalysis,
        prioritize_by_intent: bool
    ) -> Dict[str, List[Document]]:
        """Group documents by content type and prioritize based on query intent."""
        grouped = {"text": [], "image": [], "table": [], "multimodal": []}
        
        for doc in documents:
            content_type = doc.metadata.get("content_type", "text")
            
            if content_type in ["image_caption", "image_analysis"]:
                grouped["image"].append(doc)
            elif content_type in ["table_markdown", "table_context"]:
                grouped["table"].append(doc)
            elif content_type == "multimodal_chunk":
                grouped["multimodal"].append(doc)
            else:
                grouped["text"].append(doc)
        
        # Prioritize based on query intent
        if prioritize_by_intent:
            content_weights = query_analysis.content_type_weights
            
            for content_type, docs in grouped.items():
                weight_key = "multimodal_chunk" if content_type == "multimodal" else content_type
                weight = content_weights.get(weight_key, 1.0)
                
                # Sort by weighted relevance score
                docs.sort(key=lambda d: d.metadata.get("relevance_score", 0) * weight, reverse=True)
        
        return grouped
    
    def _assemble_enhanced_text_content(
        self,
        text_docs: List[Document],
        max_length: int,
        query_analysis: MultimodalQueryAnalysis
    ) -> Dict[str, Any]:
        """Assemble enhanced text content with query-aware prioritization."""
        if not text_docs:
            return {"combined_text": "", "sources": [], "chunks": [], "total_length": 0}
        
        # Use existing context assembler as base
        base_context = self.context_assembler._assemble_text_context(text_docs, max_length)
        
        # Enhance with query-specific information
        enhanced_chunks = []
        for doc in text_docs[:10]:  # Limit to top 10 for performance
            chunk_info = {
                "content": doc.page_content,
                "source": doc.metadata.get("source", "unknown"),
                "page_num": doc.metadata.get("page_num"),
                "relevance_score": doc.metadata.get("relevance_score", 0),
                "content_type": doc.metadata.get("content_type", "text"),
                "chunk_index": doc.metadata.get("chunk_index"),
                "query_relevance": self._calculate_query_relevance(doc, query_analysis)
            }
            enhanced_chunks.append(chunk_info)
        
        return {
            **base_context,
            "chunks": enhanced_chunks,
            "query_aligned": True,
            "prioritization_method": "query_intent"
        }
    
    def _assemble_enhanced_image_content(
        self,
        image_docs: List[Document],
        query_analysis: MultimodalQueryAnalysis
    ) -> Dict[str, Any]:
        """Assemble enhanced image content with detailed metadata."""
        if not image_docs:
            return {"images": [], "descriptions": [], "total_images": 0}
        
        enhanced_images = []
        descriptions = []
        
        for doc in image_docs:
            content_hash = doc.metadata.get("content_hash")
            if not content_hash:
                continue
            
            # Get image data from storage
            image_data = self.storage.retrieve_image(content_hash)
            if not image_data:
                continue
            
            data = image_data.get("data", {})
            
            image_info = {
                "content_hash": content_hash,
                "description": doc.page_content,
                "caption": data.get("caption", ""),
                "vision_analysis": data.get("vision_analysis", {}),
                "metadata": {
                    "page_num": doc.metadata.get("page_num"),
                    "image_index": doc.metadata.get("image_index"),
                    "format": doc.metadata.get("image_format"),
                    "dimensions": doc.metadata.get("image_dimensions"),
                    "bbox": doc.metadata.get("bbox"),
                    "relevance_score": doc.metadata.get("relevance_score", 0),
                    "query_relevance": self._calculate_query_relevance(doc, query_analysis)
                },
                "spatial_context": self._get_image_spatial_context(doc),
                "content_type": doc.metadata.get("content_type")
            }
            
            enhanced_images.append(image_info)
            descriptions.append(doc.page_content)
        
        return {
            "images": enhanced_images,
            "descriptions": descriptions,
            "combined_descriptions": "\n\n".join(descriptions),
            "total_images": len(enhanced_images),
            "query_aligned": query_analysis.has_image_references,
            "image_confidence": query_analysis.image_confidence
        }
    
    def _assemble_enhanced_table_content(
        self,
        table_docs: List[Document],
        query_analysis: MultimodalQueryAnalysis
    ) -> Dict[str, Any]:
        """Assemble enhanced table content with structured data."""
        if not table_docs:
            return {"tables": [], "combined_content": "", "total_tables": 0}
        
        enhanced_tables = []
        combined_content = []
        
        for doc in table_docs:
            content_hash = doc.metadata.get("content_hash")
            if not content_hash:
                continue
            
            # Get table data from storage
            table_data = self.storage.retrieve_table(content_hash)
            if not table_data:
                continue
            
            data = table_data.get("data", {})
            
            table_info = {
                "content_hash": content_hash,
                "markdown": data.get("markdown", ""),
                "structured_data": data.get("structured_data", {}),
                "context": data.get("context", ""),
                "description": doc.page_content,
                "metadata": {
                    "page_num": doc.metadata.get("page_num"),
                    "table_index": doc.metadata.get("table_index"),
                    "num_rows": doc.metadata.get("num_rows"),
                    "num_cols": doc.metadata.get("num_cols"),
                    "extraction_method": doc.metadata.get("extraction_method"),
                    "bbox": doc.metadata.get("bbox"),
                    "relevance_score": doc.metadata.get("relevance_score", 0),
                    "query_relevance": self._calculate_query_relevance(doc, query_analysis)
                },
                "spatial_context": self._get_table_spatial_context(doc),
                "content_type": doc.metadata.get("content_type")
            }
            
            enhanced_tables.append(table_info)
            combined_content.append(doc.page_content)
        
        return {
            "tables": enhanced_tables,
            "combined_content": "\n\n".join(combined_content),
            "total_tables": len(enhanced_tables),
            "query_aligned": query_analysis.has_table_references,
            "table_confidence": query_analysis.table_confidence
        }
    
    def _assemble_multimodal_chunks(
        self,
        multimodal_docs: List[Document],
        query_analysis: MultimodalQueryAnalysis
    ) -> List[Dict[str, Any]]:
        """Assemble multimodal chunks with comprehensive content."""
        enhanced_chunks = []
        
        for doc in multimodal_docs:
            chunk_info = {
                "content": doc.page_content,
                "metadata": doc.metadata,
                "content_types": doc.metadata.get("content_types", []),
                "spatial_relationships": doc.metadata.get("spatial_relationships", {}),
                "page_num": doc.metadata.get("page_num"),
                "chunk_type": doc.metadata.get("chunk_type"),
                "num_images": doc.metadata.get("num_images", 0),
                "num_tables": doc.metadata.get("num_tables", 0),
                "relevance_score": doc.metadata.get("relevance_score", 0),
                "query_relevance": self._calculate_query_relevance(doc, query_analysis),
                "multimodal_priority": query_analysis.prioritize_multimodal_chunks
            }
            enhanced_chunks.append(chunk_info)
        
        # Sort by query relevance if prioritizing multimodal chunks
        if query_analysis.prioritize_multimodal_chunks:
            enhanced_chunks.sort(key=lambda x: x["query_relevance"], reverse=True)
        
        return enhanced_chunks
    
    def _calculate_query_relevance(self, doc: Document, query_analysis: MultimodalQueryAnalysis) -> float:
        """Calculate query-specific relevance score for a document."""
        base_score = doc.metadata.get("relevance_score", 0)
        content_type = doc.metadata.get("content_type", "text")
        
        # Apply content type weights from query analysis
        if content_type in ["image_caption", "image_analysis"]:
            weight = query_analysis.content_type_weights.get("image", 1.0)
        elif content_type in ["table_markdown", "table_context"]:
            weight = query_analysis.content_type_weights.get("table", 1.0)
        elif content_type == "multimodal_chunk":
            weight = query_analysis.content_type_weights.get("multimodal_chunk", 1.0)
        else:
            weight = query_analysis.content_type_weights.get("text", 1.0)
        
        return base_score * weight
    
    def _get_image_spatial_context(self, doc: Document) -> Dict[str, Any]:
        """Get spatial context for an image."""
        return {
            "page_num": doc.metadata.get("page_num"),
            "bbox": doc.metadata.get("bbox"),
            "image_index": doc.metadata.get("image_index"),
            "spatial_relationships": doc.metadata.get("spatial_relationships", {})
        }
    
    def _get_table_spatial_context(self, doc: Document) -> Dict[str, Any]:
        """Get spatial context for a table."""
        return {
            "page_num": doc.metadata.get("page_num"),
            "bbox": doc.metadata.get("bbox"),
            "table_index": doc.metadata.get("table_index"),
            "spatial_relationships": doc.metadata.get("spatial_relationships", {})
        }
    
    def _build_spatial_relationships(self, documents: List[Document]) -> Dict[str, Any]:
        """Build spatial relationships between content elements."""
        # Use existing context assembler spatial mapping as base
        spatial_context = self.context_assembler._create_spatial_context_map(documents)
        
        # Enhance with additional relationship analysis
        enhanced_relationships = {
            **spatial_context,
            "content_proximity": self._analyze_content_proximity(documents),
            "page_layout_analysis": self._analyze_page_layouts(documents)
        }
        
        return enhanced_relationships
    
    def _analyze_content_proximity(self, documents: List[Document]) -> List[Dict[str, Any]]:
        """Analyze proximity relationships between different content types."""
        proximity_relationships = []
        
        # Group documents by page
        by_page = {}
        for doc in documents:
            page_num = doc.metadata.get("page_num")
            if page_num:
                if page_num not in by_page:
                    by_page[page_num] = []
                by_page[page_num].append(doc)
        
        # Analyze relationships within each page
        for page_num, page_docs in by_page.items():
            for i, doc1 in enumerate(page_docs):
                for doc2 in page_docs[i+1:]:
                    relationship = self._calculate_spatial_relationship(doc1, doc2)
                    if relationship:
                        proximity_relationships.append(relationship)
        
        return proximity_relationships
    
    def _calculate_spatial_relationship(self, doc1: Document, doc2: Document) -> Optional[Dict[str, Any]]:
        """Calculate spatial relationship between two documents."""
        bbox1 = doc1.metadata.get("bbox")
        bbox2 = doc2.metadata.get("bbox")
        
        if not bbox1 or not bbox2:
            return None
        
        # Simple spatial relationship calculation
        # This could be enhanced with more sophisticated spatial analysis
        return {
            "doc1_type": doc1.metadata.get("content_type"),
            "doc2_type": doc2.metadata.get("content_type"),
            "page_num": doc1.metadata.get("page_num"),
            "relationship": "adjacent",  # Simplified for now
            "confidence": 0.5
        }
    
    def _analyze_page_layouts(self, documents: List[Document]) -> Dict[str, Any]:
        """Analyze page layouts and content organization."""
        layout_analysis = {}
        
        # Group by page and analyze layout
        by_page = {}
        for doc in documents:
            page_num = doc.metadata.get("page_num")
            if page_num:
                if page_num not in by_page:
                    by_page[page_num] = {"text": [], "images": [], "tables": []}
                
                content_type = doc.metadata.get("content_type", "text")
                if content_type in ["image_caption", "image_analysis"]:
                    by_page[page_num]["images"].append(doc)
                elif content_type in ["table_markdown", "table_context"]:
                    by_page[page_num]["tables"].append(doc)
                else:
                    by_page[page_num]["text"].append(doc)
        
        for page_num, content in by_page.items():
            layout_analysis[f"page_{page_num}"] = {
                "text_elements": len(content["text"]),
                "image_elements": len(content["images"]),
                "table_elements": len(content["tables"]),
                "layout_type": self._determine_layout_type(content)
            }
        
        return layout_analysis
    
    def _determine_layout_type(self, page_content: Dict[str, List]) -> str:
        """Determine the layout type of a page."""
        text_count = len(page_content["text"])
        image_count = len(page_content["images"])
        table_count = len(page_content["tables"])
        
        if image_count > 0 and table_count > 0:
            return "mixed_multimodal"
        elif image_count > 0:
            return "image_rich"
        elif table_count > 0:
            return "data_rich"
        else:
            return "text_only"
    
    def _build_content_relationships(self, documents: List[Document]) -> List[Dict[str, Any]]:
        """Build content relationships between different elements."""
        relationships = []
        
        # Analyze content references and connections
        for doc in documents:
            content = doc.page_content.lower()
            
            # Look for references to other content types
            if any(ref in content for ref in ["figure", "image", "picture"]):
                relationships.append({
                    "source_doc": doc.metadata.get("content_hash"),
                    "relationship_type": "references_image",
                    "confidence": 0.7
                })
            
            if any(ref in content for ref in ["table", "data", "chart"]):
                relationships.append({
                    "source_doc": doc.metadata.get("content_hash"),
                    "relationship_type": "references_table",
                    "confidence": 0.7
                })
        
        return relationships
    
    def _build_cross_references(self, documents: List[Document]) -> Dict[str, List[str]]:
        """Build cross-references between content elements."""
        cross_refs = {}
        
        for doc in documents:
            content_hash = doc.metadata.get("content_hash")
            if content_hash:
                cross_refs[content_hash] = []
                
                # Find references to other content
                content = doc.page_content.lower()
                for other_doc in documents:
                    other_hash = other_doc.metadata.get("content_hash")
                    if other_hash and other_hash != content_hash:
                        # Simple reference detection (could be enhanced)
                        if any(keyword in content for keyword in ["see", "refer", "shown", "above", "below"]):
                            cross_refs[content_hash].append(other_hash)
        
        return cross_refs
    
    def _create_enhanced_metadata(
        self,
        query_analysis: MultimodalQueryAnalysis,
        text_content: Dict[str, Any],
        image_content: Dict[str, Any],
        table_content: Dict[str, Any],
        multimodal_chunks: List[Dict[str, Any]],
        spatial_relationships: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create enhanced metadata for the context."""
        return {
            "query_type": query_analysis.query_type.value,
            "query_confidence": query_analysis.confidence_score,
            "content_intents": [intent.value for intent in query_analysis.content_intents],
            "total_text_chunks": len(text_content.get("chunks", [])),
            "total_images": image_content.get("total_images", 0),
            "total_tables": table_content.get("total_tables", 0),
            "total_multimodal_chunks": len(multimodal_chunks),
            "has_spatial_relationships": bool(spatial_relationships.get("spatial_relationships_available")),
            "context_richness_score": self._calculate_context_richness(
                text_content, image_content, table_content, multimodal_chunks
            ),
            "multimodal_alignment": {
                "image_aligned": image_content.get("query_aligned", False),
                "table_aligned": table_content.get("query_aligned", False),
                "overall_alignment": query_analysis.confidence_score
            }
        }
    
    def _calculate_context_richness(
        self,
        text_content: Dict[str, Any],
        image_content: Dict[str, Any],
        table_content: Dict[str, Any],
        multimodal_chunks: List[Dict[str, Any]]
    ) -> float:
        """Calculate a richness score for the assembled context."""
        text_score = min(1.0, len(text_content.get("chunks", [])) / 10)
        image_score = min(1.0, image_content.get("total_images", 0) / 5)
        table_score = min(1.0, table_content.get("total_tables", 0) / 3)
        multimodal_score = min(1.0, len(multimodal_chunks) / 5)
        
        return (text_score + image_score + table_score + multimodal_score) / 4
    
    def _create_source_attribution(self, documents: List[Document]) -> Dict[str, Any]:
        """Create comprehensive source attribution."""
        sources = {}
        
        for doc in documents:
            source = doc.metadata.get("source", "unknown")
            if source not in sources:
                sources[source] = {
                    "document_path": source,
                    "content_types": set(),
                    "page_numbers": set(),
                    "total_chunks": 0
                }
            
            sources[source]["content_types"].add(doc.metadata.get("content_type", "text"))
            if doc.metadata.get("page_num"):
                sources[source]["page_numbers"].add(doc.metadata.get("page_num"))
            sources[source]["total_chunks"] += 1
        
        # Convert sets to lists for JSON serialization
        for source_info in sources.values():
            source_info["content_types"] = list(source_info["content_types"])
            source_info["page_numbers"] = sorted(list(source_info["page_numbers"]))
        
        return sources

    def _format_enhanced_context_for_llm(
        self,
        query: str,
        query_analysis: MultimodalQueryAnalysis,
        text_content: Dict[str, Any],
        image_content: Dict[str, Any],
        table_content: Dict[str, Any],
        multimodal_chunks: List[Dict[str, Any]],
        spatial_relationships: Dict[str, Any]
    ) -> str:
        """Format enhanced context for LLM consumption with rich multimodal information."""
        context_parts = []

        # Add query context
        context_parts.append(f"Query: {query}")
        context_parts.append(f"Query Type: {query_analysis.query_type.value}")

        if query_analysis.content_intents:
            intents = [intent.value for intent in query_analysis.content_intents]
            context_parts.append(f"Detected Intent: {', '.join(intents)}")

        # Add multimodal chunks first if prioritized
        if query_analysis.prioritize_multimodal_chunks and multimodal_chunks:
            context_parts.append("\n=== MULTIMODAL CONTENT ===")
            for i, chunk in enumerate(multimodal_chunks[:3]):  # Top 3 multimodal chunks
                context_parts.append(f"Multimodal Section {i+1} (Page {chunk.get('page_num', 'unknown')}):")
                context_parts.append(chunk["content"])

                if chunk.get("num_images", 0) > 0:
                    context_parts.append(f"  [Contains {chunk['num_images']} image(s)]")
                if chunk.get("num_tables", 0) > 0:
                    context_parts.append(f"  [Contains {chunk['num_tables']} table(s)]")

        # Add text content
        if text_content.get("combined_text"):
            context_parts.append("\n=== TEXT CONTENT ===")
            context_parts.append(text_content["combined_text"])

        # Add image content if relevant
        if image_content.get("images") and query_analysis.has_image_references:
            context_parts.append("\n=== IMAGE CONTENT ===")
            for i, image in enumerate(image_content["images"][:5]):  # Top 5 images
                context_parts.append(f"Image {i+1} (Page {image['metadata'].get('page_num', 'unknown')}):")
                if image.get("caption"):
                    context_parts.append(f"  Caption: {image['caption']}")
                if image.get("description"):
                    context_parts.append(f"  Description: {image['description']}")

                vision_analysis = image.get("vision_analysis", {})
                if vision_analysis.get("description"):
                    context_parts.append(f"  Analysis: {vision_analysis['description']}")

        # Add table content if relevant
        if table_content.get("tables") and query_analysis.has_table_references:
            context_parts.append("\n=== TABLE CONTENT ===")
            for i, table in enumerate(table_content["tables"][:3]):  # Top 3 tables
                context_parts.append(f"Table {i+1} (Page {table['metadata'].get('page_num', 'unknown')}):")
                if table.get("context"):
                    context_parts.append(f"  Context: {table['context']}")
                if table.get("markdown"):
                    context_parts.append(f"  Data:\n{table['markdown']}")

                metadata = table.get("metadata", {})
                if metadata.get("num_rows") and metadata.get("num_cols"):
                    context_parts.append(f"  [Table size: {metadata['num_rows']} rows × {metadata['num_cols']} columns]")

        # Add spatial relationships if available and relevant
        if spatial_relationships.get("spatial_relationships_available") and query_analysis.has_spatial_references:
            context_parts.append("\n=== SPATIAL RELATIONSHIPS ===")
            pages = spatial_relationships.get("pages", [])
            for page in pages[:3]:  # Top 3 pages
                page_num = page.get("page_num", "unknown")
                elements = page.get("content_elements", [])
                if elements:
                    context_parts.append(f"Page {page_num} Layout: {len(elements)} content elements")

        # Add metadata summary
        context_parts.append(f"\n=== CONTEXT SUMMARY ===")
        context_parts.append(f"Total Sources: {len(text_content.get('sources', []))}")
        if image_content.get("total_images", 0) > 0:
            context_parts.append(f"Images Available: {image_content['total_images']}")
        if table_content.get("total_tables", 0) > 0:
            context_parts.append(f"Tables Available: {table_content['total_tables']}")
        if multimodal_chunks:
            context_parts.append(f"Multimodal Sections: {len(multimodal_chunks)}")

        return "\n".join(context_parts)

    def _create_structured_context(
        self,
        text_content: Dict[str, Any],
        image_content: Dict[str, Any],
        table_content: Dict[str, Any],
        multimodal_chunks: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create structured context for programmatic access."""
        return {
            "text": {
                "content": text_content.get("combined_text", ""),
                "chunks": text_content.get("chunks", []),
                "sources": text_content.get("sources", [])
            },
            "images": {
                "descriptions": image_content.get("descriptions", []),
                "metadata": [img.get("metadata", {}) for img in image_content.get("images", [])],
                "total_count": image_content.get("total_images", 0)
            },
            "tables": {
                "content": table_content.get("combined_content", ""),
                "structured_data": [tbl.get("structured_data", {}) for tbl in table_content.get("tables", [])],
                "metadata": [tbl.get("metadata", {}) for tbl in table_content.get("tables", [])],
                "total_count": table_content.get("total_tables", 0)
            },
            "multimodal": {
                "chunks": multimodal_chunks,
                "total_count": len(multimodal_chunks)
            }
        }

    def _create_fallback_context(self, query: str, documents: List[Document]) -> EnhancedMultimodalContext:
        """Create a fallback context for error cases."""
        from app.services.multimodal_query_analyzer import QueryType, MultimodalQueryAnalysis

        fallback_analysis = MultimodalQueryAnalysis(
            query=query,
            query_type=QueryType.TEXT_ONLY,
            confidence_score=0.0,
            content_intents=[],
            has_image_references=False,
            has_table_references=False,
            has_visual_references=False,
            has_data_references=False,
            has_spatial_references=False,
            image_confidence=0.0,
            table_confidence=0.0,
            visual_confidence=0.0,
            data_confidence=0.0,
            spatial_confidence=0.0,
            complexity_level="simple",
            word_count=len(query.split()),
            question_type="statement",
            recommended_k_multiplier=1.0,
            prioritize_multimodal_chunks=False,
            content_type_weights={"text": 1.0, "image": 0.0, "table": 0.0, "multimodal_chunk": 0.0},
            detected_keywords=[],
            query_intent_description="Fallback text-only analysis"
        )

        # Basic text content from documents
        text_content = " ".join([doc.page_content for doc in documents[:5]])

        return EnhancedMultimodalContext(
            query=query,
            query_analysis=fallback_analysis,
            text_content={"combined_text": text_content, "sources": [], "chunks": []},
            image_content={"images": [], "descriptions": [], "total_images": 0},
            table_content={"tables": [], "combined_content": "", "total_tables": 0},
            multimodal_chunks=[],
            spatial_relationships={},
            content_relationships=[],
            cross_references={},
            context_metadata={"fallback": True},
            source_attribution={},
            formatted_context=f"Query: {query}\n\nText Content:\n{text_content}",
            structured_context={}
        )


# Global service instance
_enhanced_multimodal_context_service = None

def get_enhanced_multimodal_context_service() -> EnhancedMultimodalContextService:
    """Get the global enhanced multimodal context service instance."""
    global _enhanced_multimodal_context_service
    if _enhanced_multimodal_context_service is None:
        _enhanced_multimodal_context_service = EnhancedMultimodalContextService()
    return _enhanced_multimodal_context_service

"""
Enhanced Context Formatter

Advanced context formatting service for LLM consumption that properly structures
multimodal content with clear content type indicators, spatial relationships,
and optimized presentation for different query types.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from app.services.multimodal_query_analyzer import QueryType, MultimodalQueryAnalysis
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class ContextFormat(Enum):
    """Different context formatting styles."""
    COMPREHENSIVE = "comprehensive"  # Full multimodal context with all details
    FOCUSED = "focused"  # Query-focused context with relevant content prioritized
    STRUCTURED = "structured"  # Highly structured format with clear sections
    NARRATIVE = "narrative"  # Narrative format that flows naturally
    TECHNICAL = "technical"  # Technical format with detailed metadata


@dataclass
class FormattingOptions:
    """Options for context formatting."""
    format_style: ContextFormat = ContextFormat.COMPREHENSIVE
    max_text_length: int = 4000
    max_images: int = 5
    max_tables: int = 3
    include_metadata: bool = True
    include_spatial_info: bool = True
    include_source_attribution: bool = True
    prioritize_by_query: bool = True
    add_content_indicators: bool = True
    use_markdown_formatting: bool = True


class EnhancedContextFormatter:
    """
    Enhanced context formatter for multimodal content.
    
    This formatter provides sophisticated formatting of multimodal context
    for optimal LLM consumption, with different formatting styles based
    on query type and content characteristics.
    """
    
    def __init__(self):
        """Initialize the enhanced context formatter."""
        logger.info("Initialized EnhancedContextFormatter")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def format_enhanced_context(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis] = None,
        formatting_options: Optional[FormattingOptions] = None
    ) -> str:
        """
        Format enhanced multimodal context for LLM consumption.
        
        Args:
            enhanced_context: Enhanced multimodal context object
            query_analysis: Query analysis for formatting optimization
            formatting_options: Formatting options and preferences
            
        Returns:
            Formatted context string optimized for LLM
        """
        try:
            # Use default options if not provided
            if formatting_options is None:
                formatting_options = FormattingOptions()
            
            # Determine optimal format style based on query
            if query_analysis and formatting_options.prioritize_by_query:
                formatting_options.format_style = self._determine_optimal_format_style(query_analysis)
            
            logger.debug(f"Formatting context with style: {formatting_options.format_style.value}")
            
            # Format based on selected style
            if formatting_options.format_style == ContextFormat.COMPREHENSIVE:
                return self._format_comprehensive(enhanced_context, query_analysis, formatting_options)
            elif formatting_options.format_style == ContextFormat.FOCUSED:
                return self._format_focused(enhanced_context, query_analysis, formatting_options)
            elif formatting_options.format_style == ContextFormat.STRUCTURED:
                return self._format_structured(enhanced_context, query_analysis, formatting_options)
            elif formatting_options.format_style == ContextFormat.NARRATIVE:
                return self._format_narrative(enhanced_context, query_analysis, formatting_options)
            elif formatting_options.format_style == ContextFormat.TECHNICAL:
                return self._format_technical(enhanced_context, query_analysis, formatting_options)
            else:
                return self._format_comprehensive(enhanced_context, query_analysis, formatting_options)
            
        except Exception as e:
            logger.error(f"Error formatting enhanced context: {e}")
            return self._format_fallback(enhanced_context)
    
    def _determine_optimal_format_style(self, query_analysis: MultimodalQueryAnalysis) -> ContextFormat:
        """Determine optimal formatting style based on query analysis."""
        query_type = query_analysis.query_type
        
        if query_type == QueryType.IMAGE_FOCUSED:
            return ContextFormat.FOCUSED
        elif query_type == QueryType.TABLE_FOCUSED:
            return ContextFormat.STRUCTURED
        elif query_type == QueryType.DATA_ANALYSIS:
            return ContextFormat.TECHNICAL
        elif query_type == QueryType.MULTIMODAL_MIXED:
            return ContextFormat.COMPREHENSIVE
        elif query_type == QueryType.SPATIAL_RELATIONSHIP:
            return ContextFormat.STRUCTURED
        else:
            return ContextFormat.NARRATIVE
    
    def _format_comprehensive(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Format comprehensive multimodal context with all content types."""
        sections = []
        
        # Add query context header
        sections.append(self._create_query_header(enhanced_context.query, query_analysis, options))
        
        # Add multimodal chunks first if they exist and are prioritized
        if enhanced_context.multimodal_chunks and query_analysis and query_analysis.prioritize_multimodal_chunks:
            sections.append(self._format_multimodal_chunks_section(enhanced_context.multimodal_chunks, options))
        
        # Add text content
        if enhanced_context.text_content.get("combined_text"):
            sections.append(self._format_text_section(enhanced_context.text_content, options))
        
        # Add image content
        if enhanced_context.image_content.get("images"):
            sections.append(self._format_images_section(enhanced_context.image_content, options))
        
        # Add table content
        if enhanced_context.table_content.get("tables"):
            sections.append(self._format_tables_section(enhanced_context.table_content, options))
        
        # Add spatial relationships if available
        if options.include_spatial_info and enhanced_context.spatial_relationships:
            sections.append(self._format_spatial_section(enhanced_context.spatial_relationships, options))
        
        # Add context summary
        sections.append(self._create_context_summary(enhanced_context, options))
        
        return "\n\n".join(sections)
    
    def _format_focused(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Format focused context prioritizing query-relevant content."""
        sections = []
        
        # Add focused query header
        sections.append(self._create_focused_query_header(enhanced_context.query, query_analysis))
        
        if query_analysis:
            # Prioritize content based on query analysis
            if query_analysis.has_image_references and enhanced_context.image_content.get("images"):
                sections.append(self._format_images_section(enhanced_context.image_content, options, focused=True))
            
            if query_analysis.has_table_references and enhanced_context.table_content.get("tables"):
                sections.append(self._format_tables_section(enhanced_context.table_content, options, focused=True))
            
            # Add relevant multimodal chunks
            if enhanced_context.multimodal_chunks:
                relevant_chunks = self._filter_relevant_chunks(enhanced_context.multimodal_chunks, query_analysis)
                if relevant_chunks:
                    sections.append(self._format_multimodal_chunks_section(relevant_chunks, options))
        
        # Add most relevant text content
        if enhanced_context.text_content.get("chunks"):
            top_chunks = enhanced_context.text_content["chunks"][:3]  # Top 3 chunks
            sections.append(self._format_focused_text_section(top_chunks, options))
        
        return "\n\n".join(sections)
    
    def _format_structured(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Format structured context with clear hierarchical organization."""
        sections = []
        
        # Structured header
        sections.append("# MULTIMODAL DOCUMENT ANALYSIS")
        sections.append(f"**Query:** {enhanced_context.query}")
        
        if query_analysis:
            sections.append(f"**Query Type:** {query_analysis.query_type.value}")
            sections.append(f"**Confidence:** {query_analysis.confidence_score:.2f}")
        
        # Content inventory
        sections.append("\n## CONTENT INVENTORY")
        inventory = []
        if enhanced_context.text_content.get("chunks"):
            inventory.append(f"- Text Sections: {len(enhanced_context.text_content['chunks'])}")
        if enhanced_context.image_content.get("total_images", 0) > 0:
            inventory.append(f"- Images: {enhanced_context.image_content['total_images']}")
        if enhanced_context.table_content.get("total_tables", 0) > 0:
            inventory.append(f"- Tables: {enhanced_context.table_content['total_tables']}")
        if enhanced_context.multimodal_chunks:
            inventory.append(f"- Multimodal Sections: {len(enhanced_context.multimodal_chunks)}")
        
        sections.append("\n".join(inventory))
        
        # Structured content sections
        if enhanced_context.text_content.get("combined_text"):
            sections.append("\n## TEXT CONTENT")
            sections.append(enhanced_context.text_content["combined_text"][:options.max_text_length])
        
        if enhanced_context.image_content.get("images"):
            sections.append("\n## IMAGE CONTENT")
            sections.append(self._format_structured_images(enhanced_context.image_content["images"][:options.max_images]))
        
        if enhanced_context.table_content.get("tables"):
            sections.append("\n## TABLE CONTENT")
            sections.append(self._format_structured_tables(enhanced_context.table_content["tables"][:options.max_tables]))
        
        return "\n".join(sections)
    
    def _format_narrative(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Format narrative context that flows naturally."""
        narrative_parts = []
        
        # Opening context
        narrative_parts.append(f"In response to the query '{enhanced_context.query}', here is the relevant information:")
        
        # Weave content together narratively
        text_content = enhanced_context.text_content.get("combined_text", "")
        if text_content:
            narrative_parts.append(f"\nThe document content indicates: {text_content[:options.max_text_length]}")
        
        # Add images narratively
        images = enhanced_context.image_content.get("images", [])
        if images:
            narrative_parts.append(f"\nThe document contains {len(images)} relevant image(s):")
            for i, image in enumerate(images[:options.max_images]):
                if image.get("description"):
                    narrative_parts.append(f"Image {i+1}: {image['description']}")
        
        # Add tables narratively
        tables = enhanced_context.table_content.get("tables", [])
        if tables:
            narrative_parts.append(f"\nThe document includes {len(tables)} relevant table(s) with data:")
            for i, table in enumerate(tables[:options.max_tables]):
                if table.get("context"):
                    narrative_parts.append(f"Table {i+1}: {table['context']}")
                if table.get("markdown"):
                    narrative_parts.append(f"Data:\n{table['markdown']}")
        
        # Add multimodal sections narratively
        if enhanced_context.multimodal_chunks:
            narrative_parts.append(f"\nAdditionally, there are {len(enhanced_context.multimodal_chunks)} sections that combine multiple content types:")
            for chunk in enhanced_context.multimodal_chunks[:2]:
                narrative_parts.append(chunk["content"][:300] + "...")
        
        return "\n".join(narrative_parts)
    
    def _format_technical(
        self,
        enhanced_context,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Format technical context with detailed metadata and structure."""
        sections = []
        
        # Technical header with metadata
        sections.append("=== TECHNICAL MULTIMODAL ANALYSIS ===")
        sections.append(f"Query: {enhanced_context.query}")
        
        if query_analysis:
            sections.append(f"Query Classification: {query_analysis.query_type.value}")
            sections.append(f"Confidence Score: {query_analysis.confidence_score:.3f}")
            sections.append(f"Content Intents: {[intent.value for intent in query_analysis.content_intents]}")
            sections.append(f"Detected Keywords: {query_analysis.detected_keywords}")
        
        # Context metadata
        metadata = enhanced_context.context_metadata
        sections.append(f"\nContext Metadata:")
        sections.append(f"- Total Text Chunks: {metadata.get('total_text_chunks', 0)}")
        sections.append(f"- Total Images: {metadata.get('total_images', 0)}")
        sections.append(f"- Total Tables: {metadata.get('total_tables', 0)}")
        sections.append(f"- Context Richness Score: {metadata.get('context_richness_score', 0):.3f}")
        sections.append(f"- Spatial Relationships Available: {metadata.get('has_spatial_relationships', False)}")
        
        # Technical content sections with metadata
        if enhanced_context.text_content.get("chunks"):
            sections.append("\n--- TEXT ANALYSIS ---")
            for i, chunk in enumerate(enhanced_context.text_content["chunks"][:3]):
                sections.append(f"Chunk {i+1} (Relevance: {chunk.get('relevance_score', 0):.3f}):")
                sections.append(f"Source: {chunk.get('source', 'unknown')}")
                sections.append(f"Page: {chunk.get('page_num', 'unknown')}")
                sections.append(f"Content: {chunk['content'][:200]}...")
        
        if enhanced_context.image_content.get("images"):
            sections.append("\n--- IMAGE ANALYSIS ---")
            for i, image in enumerate(enhanced_context.image_content["images"][:options.max_images]):
                metadata = image.get("metadata", {})
                sections.append(f"Image {i+1}:")
                sections.append(f"- Page: {metadata.get('page_num', 'unknown')}")
                sections.append(f"- Format: {metadata.get('format', 'unknown')}")
                sections.append(f"- Dimensions: {metadata.get('dimensions', 'unknown')}")
                sections.append(f"- Relevance: {metadata.get('relevance_score', 0):.3f}")
                if image.get("description"):
                    sections.append(f"- Description: {image['description']}")
        
        if enhanced_context.table_content.get("tables"):
            sections.append("\n--- TABLE ANALYSIS ---")
            for i, table in enumerate(enhanced_context.table_content["tables"][:options.max_tables]):
                metadata = table.get("metadata", {})
                sections.append(f"Table {i+1}:")
                sections.append(f"- Page: {metadata.get('page_num', 'unknown')}")
                sections.append(f"- Size: {metadata.get('num_rows', 'unknown')} × {metadata.get('num_cols', 'unknown')}")
                sections.append(f"- Extraction Method: {metadata.get('extraction_method', 'unknown')}")
                sections.append(f"- Relevance: {metadata.get('relevance_score', 0):.3f}")
                if table.get("markdown"):
                    sections.append(f"- Data Preview:\n{table['markdown'][:300]}...")
        
        return "\n".join(sections)
    
    def _create_query_header(
        self,
        query: str,
        query_analysis: Optional[MultimodalQueryAnalysis],
        options: FormattingOptions
    ) -> str:
        """Create comprehensive query header."""
        header_parts = [f"Query: {query}"]
        
        if query_analysis and options.include_metadata:
            header_parts.append(f"Query Type: {query_analysis.query_type.value}")
            if query_analysis.content_intents:
                intents = [intent.value for intent in query_analysis.content_intents]
                header_parts.append(f"Content Focus: {', '.join(intents)}")
        
        return "\n".join(header_parts)
    
    def _create_focused_query_header(self, query: str, query_analysis: Optional[MultimodalQueryAnalysis]) -> str:
        """Create focused query header."""
        if query_analysis:
            return f"Query: {query} (Focus: {query_analysis.query_type.value})"
        return f"Query: {query}"
    
    def _format_text_section(self, text_content: Dict[str, Any], options: FormattingOptions) -> str:
        """Format text content section."""
        if options.use_markdown_formatting:
            return f"## Text Content\n\n{text_content.get('combined_text', '')[:options.max_text_length]}"
        return f"Text Content:\n{text_content.get('combined_text', '')[:options.max_text_length]}"
    
    def _format_focused_text_section(self, chunks: List[Dict[str, Any]], options: FormattingOptions) -> str:
        """Format focused text section with top chunks."""
        if not chunks:
            return ""
        
        section_parts = ["## Most Relevant Text Content"]
        for i, chunk in enumerate(chunks):
            section_parts.append(f"**Section {i+1}:** {chunk['content'][:300]}...")
        
        return "\n\n".join(section_parts)
    
    def _format_images_section(
        self,
        image_content: Dict[str, Any],
        options: FormattingOptions,
        focused: bool = False
    ) -> str:
        """Format images section."""
        images = image_content.get("images", [])[:options.max_images]
        if not images:
            return ""
        
        section_parts = ["## Image Content" if not focused else "## Relevant Images"]
        
        for i, image in enumerate(images):
            image_parts = [f"**Image {i+1}:**"]
            
            if image.get("caption"):
                image_parts.append(f"Caption: {image['caption']}")
            
            if image.get("description"):
                image_parts.append(f"Description: {image['description']}")
            
            vision_analysis = image.get("vision_analysis", {})
            if vision_analysis.get("description"):
                image_parts.append(f"Analysis: {vision_analysis['description']}")
            
            if options.include_metadata:
                metadata = image.get("metadata", {})
                if metadata.get("page_num"):
                    image_parts.append(f"(Page {metadata['page_num']})")
            
            section_parts.append("\n".join(image_parts))
        
        return "\n\n".join(section_parts)
    
    def _format_tables_section(
        self,
        table_content: Dict[str, Any],
        options: FormattingOptions,
        focused: bool = False
    ) -> str:
        """Format tables section."""
        tables = table_content.get("tables", [])[:options.max_tables]
        if not tables:
            return ""
        
        section_parts = ["## Table Content" if not focused else "## Relevant Tables"]
        
        for i, table in enumerate(tables):
            table_parts = [f"**Table {i+1}:**"]
            
            if table.get("context"):
                table_parts.append(f"Context: {table['context']}")
            
            if table.get("markdown"):
                table_parts.append(f"Data:\n{table['markdown']}")
            
            if options.include_metadata:
                metadata = table.get("metadata", {})
                meta_info = []
                if metadata.get("page_num"):
                    meta_info.append(f"Page {metadata['page_num']}")
                if metadata.get("num_rows") and metadata.get("num_cols"):
                    meta_info.append(f"{metadata['num_rows']}×{metadata['num_cols']}")
                if meta_info:
                    table_parts.append(f"({', '.join(meta_info)})")
            
            section_parts.append("\n".join(table_parts))
        
        return "\n\n".join(section_parts)
    
    def _format_multimodal_chunks_section(self, chunks: List[Dict[str, Any]], options: FormattingOptions) -> str:
        """Format multimodal chunks section."""
        if not chunks:
            return ""
        
        section_parts = ["## Multimodal Content Sections"]
        
        for i, chunk in enumerate(chunks[:3]):  # Top 3 chunks
            chunk_parts = [f"**Section {i+1} (Page {chunk.get('page_num', 'unknown')}):**"]
            chunk_parts.append(chunk["content"])
            
            if chunk.get("num_images", 0) > 0:
                chunk_parts.append(f"[Contains {chunk['num_images']} image(s)]")
            if chunk.get("num_tables", 0) > 0:
                chunk_parts.append(f"[Contains {chunk['num_tables']} table(s)]")
            
            section_parts.append("\n".join(chunk_parts))
        
        return "\n\n".join(section_parts)
    
    def _format_spatial_section(self, spatial_relationships: Dict[str, Any], options: FormattingOptions) -> str:
        """Format spatial relationships section."""
        if not spatial_relationships.get("spatial_relationships_available"):
            return ""
        
        section_parts = ["## Spatial Context"]
        
        pages = spatial_relationships.get("pages", [])
        for page in pages[:3]:  # Top 3 pages
            page_num = page.get("page_num", "unknown")
            elements = page.get("content_elements", [])
            section_parts.append(f"Page {page_num}: {len(elements)} content elements")
        
        return "\n".join(section_parts)
    
    def _format_structured_images(self, images: List[Dict[str, Any]]) -> str:
        """Format images in structured format."""
        if not images:
            return "No images available."
        
        formatted_images = []
        for i, image in enumerate(images):
            image_info = [f"### Image {i+1}"]
            
            metadata = image.get("metadata", {})
            if metadata.get("page_num"):
                image_info.append(f"**Location:** Page {metadata['page_num']}")
            
            if image.get("description"):
                image_info.append(f"**Description:** {image['description']}")
            
            if image.get("caption"):
                image_info.append(f"**Caption:** {image['caption']}")
            
            formatted_images.append("\n".join(image_info))
        
        return "\n\n".join(formatted_images)
    
    def _format_structured_tables(self, tables: List[Dict[str, Any]]) -> str:
        """Format tables in structured format."""
        if not tables:
            return "No tables available."
        
        formatted_tables = []
        for i, table in enumerate(tables):
            table_info = [f"### Table {i+1}"]
            
            metadata = table.get("metadata", {})
            if metadata.get("page_num"):
                table_info.append(f"**Location:** Page {metadata['page_num']}")
            
            if metadata.get("num_rows") and metadata.get("num_cols"):
                table_info.append(f"**Size:** {metadata['num_rows']} rows × {metadata['num_cols']} columns")
            
            if table.get("context"):
                table_info.append(f"**Context:** {table['context']}")
            
            if table.get("markdown"):
                table_info.append(f"**Data:**\n{table['markdown']}")
            
            formatted_tables.append("\n".join(table_info))
        
        return "\n\n".join(formatted_tables)
    
    def _filter_relevant_chunks(
        self,
        chunks: List[Dict[str, Any]],
        query_analysis: MultimodalQueryAnalysis
    ) -> List[Dict[str, Any]]:
        """Filter multimodal chunks based on query relevance."""
        relevant_chunks = []
        
        for chunk in chunks:
            relevance_score = chunk.get("query_relevance", 0)
            
            # Include chunks with high relevance or specific content types
            if relevance_score > 0.5:
                relevant_chunks.append(chunk)
            elif query_analysis.has_image_references and chunk.get("num_images", 0) > 0:
                relevant_chunks.append(chunk)
            elif query_analysis.has_table_references and chunk.get("num_tables", 0) > 0:
                relevant_chunks.append(chunk)
        
        return relevant_chunks[:3]  # Top 3 relevant chunks
    
    def _create_context_summary(self, enhanced_context, options: FormattingOptions) -> str:
        """Create context summary section."""
        if not options.include_metadata:
            return ""
        
        summary_parts = ["## Context Summary"]
        
        metadata = enhanced_context.context_metadata
        summary_info = []
        
        if metadata.get("total_text_chunks", 0) > 0:
            summary_info.append(f"Text sections: {metadata['total_text_chunks']}")
        if metadata.get("total_images", 0) > 0:
            summary_info.append(f"Images: {metadata['total_images']}")
        if metadata.get("total_tables", 0) > 0:
            summary_info.append(f"Tables: {metadata['total_tables']}")
        if len(enhanced_context.multimodal_chunks) > 0:
            summary_info.append(f"Multimodal sections: {len(enhanced_context.multimodal_chunks)}")
        
        if summary_info:
            summary_parts.append(f"Content available: {', '.join(summary_info)}")
        
        if options.include_source_attribution and enhanced_context.source_attribution:
            sources = list(enhanced_context.source_attribution.keys())
            summary_parts.append(f"Sources: {len(sources)} document(s)")
        
        return "\n".join(summary_parts)
    
    def _format_fallback(self, enhanced_context) -> str:
        """Fallback formatting for error cases."""
        try:
            return enhanced_context.formatted_context
        except:
            return f"Query: {enhanced_context.query}\n\nContext formatting error occurred."


# Global formatter instance
_enhanced_context_formatter = None

def get_enhanced_context_formatter() -> EnhancedContextFormatter:
    """Get the global enhanced context formatter instance."""
    global _enhanced_context_formatter
    if _enhanced_context_formatter is None:
        _enhanced_context_formatter = EnhancedContextFormatter()
    return _enhanced_context_formatter

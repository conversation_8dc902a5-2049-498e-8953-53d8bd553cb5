"""
Enhanced Multimodal Query Analyzer

Advanced query analysis service that detects multimodal intent in user queries,
including image, table, and visual references with confidence scoring and
query type classification for better retrieval strategy selection.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """Types of queries based on multimodal intent."""
    TEXT_ONLY = "text_only"
    IMAGE_FOCUSED = "image_focused"
    TABLE_FOCUSED = "table_focused"
    VISUAL_FOCUSED = "visual_focused"
    MULTIMODAL_MIXED = "multimodal_mixed"
    DATA_ANALYSIS = "data_analysis"
    SPATIAL_RELATIONSHIP = "spatial_relationship"


class ContentIntent(Enum):
    """Specific content intents within queries."""
    DESCRIBE_IMAGE = "describe_image"
    FIND_TABLE = "find_table"
    COMPARE_DATA = "compare_data"
    VISUAL_ANALYSIS = "visual_analysis"
    SPATIAL_CONTEXT = "spatial_context"
    EXTRACT_DATA = "extract_data"
    SUMMARIZE_VISUAL = "summarize_visual"


@dataclass
class MultimodalQueryAnalysis:
    """Results of multimodal query analysis."""
    query: str
    query_type: QueryType
    confidence_score: float
    content_intents: List[ContentIntent]
    
    # Content type indicators
    has_image_references: bool
    has_table_references: bool
    has_visual_references: bool
    has_data_references: bool
    has_spatial_references: bool
    
    # Confidence scores for each content type
    image_confidence: float
    table_confidence: float
    visual_confidence: float
    data_confidence: float
    spatial_confidence: float
    
    # Query characteristics
    complexity_level: str  # "simple", "medium", "complex"
    word_count: int
    question_type: str  # "what", "how", "where", "when", "why", "show"
    
    # Retrieval recommendations
    recommended_k_multiplier: float
    prioritize_multimodal_chunks: bool
    content_type_weights: Dict[str, float]
    
    # Additional metadata
    detected_keywords: List[str]
    query_intent_description: str


class MultimodalQueryAnalyzer:
    """
    Enhanced query analyzer for multimodal content detection.
    
    This analyzer provides sophisticated detection of multimodal intent
    in user queries, enabling better retrieval strategy selection and
    content prioritization.
    """
    
    def __init__(self):
        """Initialize the multimodal query analyzer."""
        self._init_keyword_patterns()
        self._init_intent_patterns()
        logger.info("Initialized MultimodalQueryAnalyzer")
    
    def _init_keyword_patterns(self):
        """Initialize keyword patterns for content type detection."""
        self.image_keywords = {
            "direct": ["image", "picture", "photo", "figure", "diagram", "chart", "graph", "illustration"],
            "visual": ["visual", "see", "look", "view", "display", "show", "appear", "visible"],
            "descriptive": ["caption", "description", "depicts", "shows", "illustrates", "represents"],
            "analysis": ["analyze", "examine", "inspect", "observe", "identify", "recognize"]
        }
        
        self.table_keywords = {
            "direct": ["table", "data", "statistics", "numbers", "values", "dataset"],
            "structure": ["rows", "columns", "cells", "entries", "records", "fields"],
            "analysis": ["compare", "analyze", "calculate", "sum", "average", "total", "count"],
            "format": ["csv", "excel", "spreadsheet", "tabular", "structured"]
        }
        
        self.visual_keywords = {
            "action": ["show", "display", "present", "exhibit", "demonstrate", "reveal"],
            "perception": ["see", "look", "view", "observe", "watch", "examine"],
            "appearance": ["appears", "looks", "seems", "visible", "shown", "displayed"],
            "spatial": ["above", "below", "next to", "beside", "near", "around", "within"]
        }
        
        self.data_keywords = {
            "analysis": ["analyze", "compare", "evaluate", "assess", "examine", "study"],
            "extraction": ["extract", "find", "get", "obtain", "retrieve", "collect"],
            "computation": ["calculate", "compute", "determine", "measure", "quantify"],
            "aggregation": ["total", "sum", "average", "mean", "median", "count", "maximum", "minimum"]
        }
        
        self.spatial_keywords = {
            "position": ["position", "location", "placement", "arrangement", "layout"],
            "relationship": ["relationship", "connection", "association", "correlation", "link"],
            "proximity": ["near", "close", "adjacent", "neighboring", "surrounding", "nearby"],
            "direction": ["above", "below", "left", "right", "top", "bottom", "side"]
        }
    
    def _init_intent_patterns(self):
        """Initialize intent detection patterns."""
        self.intent_patterns = {
            ContentIntent.DESCRIBE_IMAGE: [
                r"describe.*(?:image|picture|figure|diagram)",
                r"what.*(?:shows?|depicts?|illustrates?)",
                r"(?:image|picture|figure).*(?:shows?|contains?|depicts?)"
            ],
            ContentIntent.FIND_TABLE: [
                r"find.*(?:table|data|statistics)",
                r"(?:table|data).*(?:shows?|contains?|includes?)",
                r"what.*(?:table|data|numbers)"
            ],
            ContentIntent.COMPARE_DATA: [
                r"compare.*(?:data|values|numbers|statistics)",
                r"difference.*(?:between|among)",
                r"(?:higher|lower|greater|less).*than"
            ],
            ContentIntent.VISUAL_ANALYSIS: [
                r"analyze.*(?:visual|image|diagram|chart)",
                r"visual.*(?:analysis|examination|inspection)",
                r"(?:examine|inspect).*(?:visual|image)"
            ],
            ContentIntent.SPATIAL_CONTEXT: [
                r"(?:where|position|location).*(?:in|on|at)",
                r"spatial.*(?:relationship|arrangement|layout)",
                r"(?:above|below|next to|beside).*(?:image|figure|table)"
            ],
            ContentIntent.EXTRACT_DATA: [
                r"extract.*(?:data|information|values|numbers)",
                r"get.*(?:data|values|numbers|statistics)",
                r"(?:data|values|numbers).*(?:from|in).*(?:table|chart)"
            ],
            ContentIntent.SUMMARIZE_VISUAL: [
                r"summarize.*(?:visual|image|diagram|chart)",
                r"overview.*(?:visual|image|figure)",
                r"(?:visual|image).*summary"
            ]
        }
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def analyze_query(self, query: str) -> MultimodalQueryAnalysis:
        """
        Analyze a query for multimodal intent and characteristics.
        
        Args:
            query: User query to analyze
            
        Returns:
            MultimodalQueryAnalysis with detailed analysis results
        """
        try:
            logger.debug(f"Analyzing query: {query[:100]}...")
            
            # Basic query characteristics
            query_lower = query.lower()
            word_count = len(query.split())
            
            # Detect content type indicators
            content_indicators = self._detect_content_indicators(query_lower)
            
            # Detect content intents
            content_intents = self._detect_content_intents(query_lower)
            
            # Determine query type
            query_type = self._determine_query_type(content_indicators, content_intents)
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(query_lower, content_indicators)
            
            # Determine complexity level
            complexity_level = self._determine_complexity_level(query, word_count)
            
            # Detect question type
            question_type = self._detect_question_type(query_lower)
            
            # Generate retrieval recommendations
            retrieval_recommendations = self._generate_retrieval_recommendations(
                query_type, confidence_scores, complexity_level
            )
            
            # Extract detected keywords
            detected_keywords = self._extract_detected_keywords(query_lower)
            
            # Generate intent description
            intent_description = self._generate_intent_description(query_type, content_intents)
            
            # Create analysis result
            analysis = MultimodalQueryAnalysis(
                query=query,
                query_type=query_type,
                confidence_score=max(confidence_scores.values()),
                content_intents=content_intents,
                
                # Content type indicators
                has_image_references=content_indicators["image"],
                has_table_references=content_indicators["table"],
                has_visual_references=content_indicators["visual"],
                has_data_references=content_indicators["data"],
                has_spatial_references=content_indicators["spatial"],
                
                # Confidence scores
                image_confidence=confidence_scores["image"],
                table_confidence=confidence_scores["table"],
                visual_confidence=confidence_scores["visual"],
                data_confidence=confidence_scores["data"],
                spatial_confidence=confidence_scores["spatial"],
                
                # Query characteristics
                complexity_level=complexity_level,
                word_count=word_count,
                question_type=question_type,
                
                # Retrieval recommendations
                recommended_k_multiplier=retrieval_recommendations["k_multiplier"],
                prioritize_multimodal_chunks=retrieval_recommendations["prioritize_multimodal"],
                content_type_weights=retrieval_recommendations["content_weights"],
                
                # Additional metadata
                detected_keywords=detected_keywords,
                query_intent_description=intent_description
            )
            
            logger.debug(f"Query analysis completed: {query_type.value}, confidence: {analysis.confidence_score:.3f}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing query: {e}")
            return self._create_fallback_analysis(query)
    
    def _detect_content_indicators(self, query_lower: str) -> Dict[str, bool]:
        """Detect content type indicators in the query."""
        indicators = {
            "image": False,
            "table": False,
            "visual": False,
            "data": False,
            "spatial": False
        }
        
        # Check for image indicators
        for category, keywords in self.image_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                indicators["image"] = True
                break
        
        # Check for table indicators
        for category, keywords in self.table_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                indicators["table"] = True
                break
        
        # Check for visual indicators
        for category, keywords in self.visual_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                indicators["visual"] = True
                break
        
        # Check for data indicators
        for category, keywords in self.data_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                indicators["data"] = True
                break
        
        # Check for spatial indicators
        for category, keywords in self.spatial_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                indicators["spatial"] = True
                break
        
        return indicators
    
    def _detect_content_intents(self, query_lower: str) -> List[ContentIntent]:
        """Detect specific content intents in the query."""
        detected_intents = []
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    detected_intents.append(intent)
                    break
        
        return detected_intents
    
    def _determine_query_type(self, indicators: Dict[str, bool], intents: List[ContentIntent]) -> QueryType:
        """Determine the overall query type based on indicators and intents."""
        # Count multimodal indicators
        multimodal_count = sum(indicators.values())
        
        if multimodal_count == 0:
            return QueryType.TEXT_ONLY
        elif multimodal_count >= 3:
            return QueryType.MULTIMODAL_MIXED
        
        # Determine specific type based on strongest indicators
        if indicators["image"] and any(intent in [ContentIntent.DESCRIBE_IMAGE, ContentIntent.VISUAL_ANALYSIS] for intent in intents):
            return QueryType.IMAGE_FOCUSED
        elif indicators["table"] and any(intent in [ContentIntent.FIND_TABLE, ContentIntent.EXTRACT_DATA] for intent in intents):
            return QueryType.TABLE_FOCUSED
        elif indicators["visual"] and any(intent in [ContentIntent.VISUAL_ANALYSIS, ContentIntent.SUMMARIZE_VISUAL] for intent in intents):
            return QueryType.VISUAL_FOCUSED
        elif indicators["data"] and any(intent in [ContentIntent.COMPARE_DATA, ContentIntent.EXTRACT_DATA] for intent in intents):
            return QueryType.DATA_ANALYSIS
        elif indicators["spatial"] and ContentIntent.SPATIAL_CONTEXT in intents:
            return QueryType.SPATIAL_RELATIONSHIP
        
        # Default based on strongest indicator
        if indicators["image"]:
            return QueryType.IMAGE_FOCUSED
        elif indicators["table"] or indicators["data"]:
            return QueryType.TABLE_FOCUSED
        elif indicators["visual"]:
            return QueryType.VISUAL_FOCUSED
        elif indicators["spatial"]:
            return QueryType.SPATIAL_RELATIONSHIP
        
        return QueryType.TEXT_ONLY
    
    def _calculate_confidence_scores(self, query_lower: str, indicators: Dict[str, bool]) -> Dict[str, float]:
        """Calculate confidence scores for each content type."""
        scores = {"image": 0.0, "table": 0.0, "visual": 0.0, "data": 0.0, "spatial": 0.0}
        
        # Calculate scores based on keyword matches and weights
        for content_type, keyword_categories in [
            ("image", self.image_keywords),
            ("table", self.table_keywords),
            ("visual", self.visual_keywords),
            ("data", self.data_keywords),
            ("spatial", self.spatial_keywords)
        ]:
            total_score = 0.0
            total_weight = 0.0
            
            for category, keywords in keyword_categories.items():
                # Weight categories differently
                category_weight = {"direct": 1.0, "visual": 0.8, "descriptive": 0.7, 
                                 "analysis": 0.9, "structure": 0.8, "format": 0.6,
                                 "action": 0.7, "perception": 0.6, "appearance": 0.5,
                                 "extraction": 0.8, "computation": 0.9, "aggregation": 0.8,
                                 "position": 0.7, "relationship": 0.8, "proximity": 0.6,
                                 "direction": 0.5}.get(category, 0.5)
                
                matches = sum(1 for keyword in keywords if keyword in query_lower)
                if matches > 0:
                    total_score += matches * category_weight
                    total_weight += category_weight
            
            if total_weight > 0:
                scores[content_type] = min(1.0, total_score / total_weight)
        
        return scores
    
    def _determine_complexity_level(self, query: str, word_count: int) -> str:
        """Determine query complexity level."""
        if word_count <= 3:
            return "simple"
        elif word_count <= 10:
            return "medium"
        else:
            return "complex"
    
    def _detect_question_type(self, query_lower: str) -> str:
        """Detect the type of question being asked."""
        question_starters = {
            "what": ["what", "which"],
            "how": ["how"],
            "where": ["where"],
            "when": ["when"],
            "why": ["why"],
            "show": ["show", "display", "present"]
        }
        
        for q_type, starters in question_starters.items():
            if any(query_lower.startswith(starter) for starter in starters):
                return q_type
        
        return "statement"
    
    def _generate_retrieval_recommendations(self, query_type: QueryType, 
                                          confidence_scores: Dict[str, float], 
                                          complexity_level: str) -> Dict[str, Any]:
        """Generate retrieval strategy recommendations."""
        # Base k multiplier based on complexity
        complexity_multipliers = {"simple": 0.8, "medium": 1.0, "complex": 1.3}
        base_multiplier = complexity_multipliers[complexity_level]
        
        # Adjust for multimodal content
        if query_type in [QueryType.MULTIMODAL_MIXED, QueryType.VISUAL_FOCUSED]:
            base_multiplier *= 1.2
        elif query_type in [QueryType.IMAGE_FOCUSED, QueryType.TABLE_FOCUSED]:
            base_multiplier *= 1.1
        
        # Content type weights for retrieval
        content_weights = {
            "text": 1.0,
            "image": confidence_scores["image"] * 1.5,
            "table": confidence_scores["table"] * 1.5,
            "multimodal_chunk": max(confidence_scores.values()) * 1.8
        }
        
        # Prioritize multimodal chunks for certain query types
        prioritize_multimodal = query_type in [
            QueryType.MULTIMODAL_MIXED, 
            QueryType.VISUAL_FOCUSED, 
            QueryType.SPATIAL_RELATIONSHIP
        ]
        
        return {
            "k_multiplier": base_multiplier,
            "prioritize_multimodal": prioritize_multimodal,
            "content_weights": content_weights
        }
    
    def _extract_detected_keywords(self, query_lower: str) -> List[str]:
        """Extract keywords that were detected in the query."""
        detected = []
        
        all_keywords = {}
        all_keywords.update({k: v for cat in self.image_keywords.values() for k, v in enumerate(cat)})
        all_keywords.update({k: v for cat in self.table_keywords.values() for k, v in enumerate(cat)})
        all_keywords.update({k: v for cat in self.visual_keywords.values() for k, v in enumerate(cat)})
        all_keywords.update({k: v for cat in self.data_keywords.values() for k, v in enumerate(cat)})
        all_keywords.update({k: v for cat in self.spatial_keywords.values() for k, v in enumerate(cat)})
        
        for keyword_list in [self.image_keywords.values(), self.table_keywords.values(), 
                           self.visual_keywords.values(), self.data_keywords.values(), 
                           self.spatial_keywords.values()]:
            for category in keyword_list:
                for keyword in category:
                    if keyword in query_lower and keyword not in detected:
                        detected.append(keyword)
        
        return detected[:10]  # Limit to top 10 keywords
    
    def _generate_intent_description(self, query_type: QueryType, intents: List[ContentIntent]) -> str:
        """Generate a human-readable description of the query intent."""
        type_descriptions = {
            QueryType.TEXT_ONLY: "Text-based information retrieval",
            QueryType.IMAGE_FOCUSED: "Image content analysis and description",
            QueryType.TABLE_FOCUSED: "Table data extraction and analysis",
            QueryType.VISUAL_FOCUSED: "Visual content examination",
            QueryType.MULTIMODAL_MIXED: "Comprehensive multimodal content analysis",
            QueryType.DATA_ANALYSIS: "Data analysis and computation",
            QueryType.SPATIAL_RELATIONSHIP: "Spatial relationship analysis"
        }
        
        base_description = type_descriptions.get(query_type, "General information retrieval")
        
        if intents:
            intent_descriptions = {
                ContentIntent.DESCRIBE_IMAGE: "image description",
                ContentIntent.FIND_TABLE: "table identification",
                ContentIntent.COMPARE_DATA: "data comparison",
                ContentIntent.VISUAL_ANALYSIS: "visual analysis",
                ContentIntent.SPATIAL_CONTEXT: "spatial context",
                ContentIntent.EXTRACT_DATA: "data extraction",
                ContentIntent.SUMMARIZE_VISUAL: "visual summarization"
            }
            
            intent_parts = [intent_descriptions.get(intent, str(intent)) for intent in intents[:3]]
            if intent_parts:
                base_description += f" with focus on {', '.join(intent_parts)}"
        
        return base_description
    
    def _create_fallback_analysis(self, query: str) -> MultimodalQueryAnalysis:
        """Create a fallback analysis for error cases."""
        return MultimodalQueryAnalysis(
            query=query,
            query_type=QueryType.TEXT_ONLY,
            confidence_score=0.0,
            content_intents=[],
            has_image_references=False,
            has_table_references=False,
            has_visual_references=False,
            has_data_references=False,
            has_spatial_references=False,
            image_confidence=0.0,
            table_confidence=0.0,
            visual_confidence=0.0,
            data_confidence=0.0,
            spatial_confidence=0.0,
            complexity_level="simple",
            word_count=len(query.split()),
            question_type="statement",
            recommended_k_multiplier=1.0,
            prioritize_multimodal_chunks=False,
            content_type_weights={"text": 1.0, "image": 0.0, "table": 0.0, "multimodal_chunk": 0.0},
            detected_keywords=[],
            query_intent_description="Fallback text-only analysis"
        )


# Global analyzer instance
_multimodal_query_analyzer = None

def get_multimodal_query_analyzer() -> MultimodalQueryAnalyzer:
    """Get the global multimodal query analyzer instance."""
    global _multimodal_query_analyzer
    if _multimodal_query_analyzer is None:
        _multimodal_query_analyzer = MultimodalQueryAnalyzer()
    return _multimodal_query_analyzer

"""
Unified RAG Service

A unified service that provides both LlamaIndex and LangChain interfaces
for multimodal RAG operations, sharing cached embeddings and vector storage
while maintaining all existing performance optimizations.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from enum import Enum

from langchain_core.documents import Document as LangChainDocument

from app.services.langchain_integration.multimodal_document_loader import MultimodalDocumentLoader
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings
from app.services.langchain_integration.multimodal_text_splitter import get_multimodal_text_splitter
from app.services.langchain_integration.chroma_vectorstore_wrapper import get_chroma_vectorstore
from app.services.langchain_integration.adaptive_retriever import get_adaptive_retriever

from app.services.enhanced_rag_service import get_enhanced_rag_service
from app.services.multimodal_document_processor import get_multimodal_document_processor
from app.services.enhanced_chunking_service import get_enhanced_chunking_service
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class RAGFramework(Enum):
    """Supported RAG frameworks."""
    LANGCHAIN = "langchain"
    LLAMAINDEX = "llamaindex"
    BOTH = "both"


class UnifiedRAGService:
    """
    Unified RAG service that provides both LlamaIndex and LangChain interfaces.
    
    This service allows users to choose between frameworks while sharing
    the same underlying infrastructure for document processing, embeddings,
    and vector storage.
    """
    
    def __init__(
        self,
        default_framework: RAGFramework = RAGFramework.BOTH,
        category: Optional[str] = None
    ):
        """
        Initialize the unified RAG service.
        
        Args:
            default_framework: Default framework to use
            category: Default category for operations
        """
        self.default_framework = default_framework
        self.category = category
        
        # Initialize shared components
        self._init_shared_components()
        
        # Initialize framework-specific components
        self._init_langchain_components()
        self._init_llamaindex_components()
        
        logger.info(f"Initialized UnifiedRAGService with framework: {default_framework.value}")
    
    def _init_shared_components(self):
        """Initialize shared components used by both frameworks."""
        try:
            # Shared document processor
            self.document_processor = get_multimodal_document_processor()
            
            # Shared embeddings (LangChain-compatible wrapper around existing Ollama embeddings)
            self.embeddings = get_default_langchain_embeddings()
            
            logger.info("Initialized shared components")
            
        except Exception as e:
            logger.error(f"Error initializing shared components: {e}")
            raise
    
    def _init_langchain_components(self):
        """Initialize LangChain-specific components."""
        try:
            # LangChain text splitter
            self.langchain_splitter = get_multimodal_text_splitter(
                chunking_strategy="adaptive",
                preserve_multimodal_relationships=True
            )
            
            # LangChain vector store
            self.langchain_vectorstore = get_chroma_vectorstore(
                category=self.category,
                embeddings=self.embeddings
            )
            
            # LangChain retriever
            self.langchain_retriever = get_adaptive_retriever(
                vectorstore=self.langchain_vectorstore,
                category=self.category,
                enable_multimodal=True
            )
            
            logger.info("Initialized LangChain components")
            
        except Exception as e:
            logger.error(f"Error initializing LangChain components: {e}")
            self.langchain_splitter = None
            self.langchain_vectorstore = None
            self.langchain_retriever = None
    
    def _init_llamaindex_components(self):
        """Initialize LlamaIndex-specific components."""
        try:
            # LlamaIndex services (existing)
            self.llamaindex_rag_service = get_enhanced_rag_service()
            self.llamaindex_chunking_service = get_enhanced_chunking_service()
            
            logger.info("Initialized LlamaIndex components")
            
        except Exception as e:
            logger.error(f"Error initializing LlamaIndex components: {e}")
            self.llamaindex_rag_service = None
            self.llamaindex_chunking_service = None
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def process_document(
        self,
        document_path: str,
        category: Optional[str] = None,
        framework: Optional[RAGFramework] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a document using the specified framework(s).
        
        Args:
            document_path: Path to the document
            category: Category for the document
            framework: Framework to use (overrides default)
            **kwargs: Additional processing arguments
            
        Returns:
            Processing results with framework-specific outputs
        """
        try:
            framework = framework or self.default_framework
            category = category or self.category
            
            logger.info(f"Processing document with framework: {framework.value}")
            
            # Use shared document processor for multimodal extraction
            multimodal_result = self.document_processor.process_document(
                document_path=document_path,
                category=category,
                **kwargs
            )
            
            result = {
                "document_path": document_path,
                "category": category,
                "framework": framework.value,
                "multimodal_content": multimodal_result,
                "langchain_processing": None,
                "llamaindex_processing": None
            }
            
            # Process with LangChain if requested
            if framework in [RAGFramework.LANGCHAIN, RAGFramework.BOTH]:
                result["langchain_processing"] = self._process_with_langchain(
                    document_path, category, multimodal_result, **kwargs
                )
            
            # Process with LlamaIndex if requested
            if framework in [RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
                result["llamaindex_processing"] = self._process_with_llamaindex(
                    document_path, category, multimodal_result, **kwargs
                )
            
            logger.info(f"Successfully processed document with {framework.value}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            raise
    
    def _process_with_langchain(
        self,
        document_path: str,
        category: str,
        multimodal_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process document using LangChain components."""
        try:
            if not self.langchain_splitter or not self.langchain_vectorstore:
                logger.warning("LangChain components not available")
                return {"error": "LangChain components not initialized"}
            
            # Load document using LangChain loader
            loader = MultimodalDocumentLoader(
                file_path=document_path,
                category=category,
                include_images=True,
                include_tables=True,
                include_multimodal_chunks=True
            )
            
            documents = loader.load()
            
            # Split documents
            split_documents = self.langchain_splitter.split_documents(documents)
            
            # Add to vector store
            doc_ids = self.langchain_vectorstore.add_documents(
                split_documents,
                category=category
            )
            
            return {
                "framework": "langchain",
                "documents_loaded": len(documents),
                "chunks_created": len(split_documents),
                "document_ids": doc_ids,
                "vectorstore_stats": self.langchain_vectorstore.get_collection_stats(category)
            }
            
        except Exception as e:
            logger.error(f"Error in LangChain processing: {e}")
            return {"error": str(e)}
    
    def _process_with_llamaindex(
        self,
        document_path: str,
        category: str,
        multimodal_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process document using LlamaIndex components."""
        try:
            if not self.llamaindex_rag_service:
                logger.warning("LlamaIndex components not available")
                return {"error": "LlamaIndex components not initialized"}
            
            # Use existing LlamaIndex processing
            result = self.llamaindex_rag_service.process_document(
                document_path,
                category,
                **kwargs
            )
            
            return {
                "framework": "llamaindex",
                "processing_result": result
            }
            
        except Exception as e:
            logger.error(f"Error in LlamaIndex processing: {e}")
            return {"error": str(e)}
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def query(
        self,
        question: str,
        category: Optional[str] = None,
        framework: Optional[RAGFramework] = None,
        k: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Query the RAG system using the specified framework(s).
        
        Args:
            question: Question to ask
            category: Category to search in
            framework: Framework to use (overrides default)
            k: Number of documents to retrieve
            **kwargs: Additional query arguments
            
        Returns:
            Query results with framework-specific outputs
        """
        try:
            framework = framework or self.default_framework
            category = category or self.category
            
            logger.info(f"Querying with framework: {framework.value}")
            
            result = {
                "question": question,
                "category": category,
                "framework": framework.value,
                "langchain_result": None,
                "llamaindex_result": None
            }
            
            # Query with LangChain if requested
            if framework in [RAGFramework.LANGCHAIN, RAGFramework.BOTH]:
                result["langchain_result"] = self._query_with_langchain(
                    question, category, k, **kwargs
                )
            
            # Query with LlamaIndex if requested
            if framework in [RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
                result["llamaindex_result"] = self._query_with_llamaindex(
                    question, category, k, **kwargs
                )
            
            return result

        except Exception as e:
            logger.error(f"Error querying: {e}")
            raise

    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def query_with_enhanced_multimodal_context(
        self,
        question: str,
        category: Optional[str] = None,
        framework: Optional[RAGFramework] = None,
        k: Optional[int] = None,
        include_spatial_relationships: bool = True,
        include_cross_references: bool = True,
        max_context_length: int = 6000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Query with enhanced multimodal context that includes images and tables.

        This method provides the most comprehensive multimodal context by using
        enhanced services that include spatial relationships, cross-references,
        and rich content assembly.

        Args:
            question: Question to ask
            category: Category to search in
            framework: Framework to use (overrides default)
            k: Number of documents to retrieve
            include_spatial_relationships: Whether to include spatial context
            include_cross_references: Whether to include cross-references
            max_context_length: Maximum length for text content
            **kwargs: Additional query arguments

        Returns:
            Query results with enhanced multimodal context
        """
        try:
            framework = framework or self.default_framework
            category = category or self.category

            logger.info(f"Enhanced multimodal query with framework: {framework.value}")

            result = {
                "question": question,
                "category": category,
                "framework": framework.value,
                "enhanced_multimodal": True,
                "langchain_result": None,
                "llamaindex_result": None,
                "context_comparison": None
            }

            # Query with LangChain if requested
            if framework in [RAGFramework.LANGCHAIN, RAGFramework.BOTH]:
                result["langchain_result"] = self._query_with_langchain(
                    question, category, k, enable_enhanced_context=True, **kwargs
                )

            # Query with LlamaIndex if requested
            if framework in [RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
                result["llamaindex_result"] = self._query_with_llamaindex(
                    question, category, k, enable_enhanced_context=True, **kwargs
                )

            # Add context comparison if both frameworks were used
            if framework == RAGFramework.BOTH:
                result["context_comparison"] = self._compare_multimodal_contexts(
                    result.get("langchain_result", {}),
                    result.get("llamaindex_result", {})
                )

            return result

        except Exception as e:
            logger.error(f"Error in enhanced multimodal query: {e}")
            raise

    def _compare_multimodal_contexts(
        self,
        langchain_result: Dict[str, Any],
        llamaindex_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compare multimodal contexts from both frameworks."""
        try:
            comparison = {
                "frameworks_compared": ["langchain", "llamaindex"],
                "both_enhanced": False,
                "content_richness_comparison": {},
                "multimodal_content_comparison": {}
            }

            # Check if both used enhanced context
            lc_enhanced = langchain_result.get("framework") == "langchain_enhanced"
            li_enhanced = llamaindex_result.get("framework") == "llamaindex_enhanced"
            comparison["both_enhanced"] = lc_enhanced and li_enhanced

            # Compare content richness
            if lc_enhanced and "enhanced_context" in langchain_result:
                lc_context = langchain_result["enhanced_context"]
                comparison["content_richness_comparison"]["langchain"] = {
                    "total_images": lc_context.get("total_images", 0),
                    "total_tables": lc_context.get("total_tables", 0),
                    "total_multimodal_chunks": lc_context.get("total_multimodal_chunks", 0),
                    "context_richness_score": lc_context.get("context_richness_score", 0),
                    "has_spatial_relationships": lc_context.get("has_spatial_relationships", False)
                }

            if li_enhanced and "multimodal_content_summary" in llamaindex_result:
                li_context = llamaindex_result["multimodal_content_summary"]
                comparison["content_richness_comparison"]["llamaindex"] = {
                    "total_images": li_context.get("total_images", 0),
                    "total_tables": li_context.get("total_tables", 0),
                    "total_multimodal_chunks": li_context.get("total_multimodal_chunks", 0),
                    "context_richness_score": li_context.get("context_richness_score", 0),
                    "has_spatial_relationships": li_context.get("has_spatial_relationships", False)
                }

            # Compare multimodal content availability
            if lc_enhanced and "multimodal_content" in langchain_result:
                lc_multimodal = langchain_result["multimodal_content"]
                comparison["multimodal_content_comparison"]["langchain"] = {
                    "text_chunks": len(lc_multimodal.get("text_chunks", [])),
                    "image_content": len(lc_multimodal.get("image_content", [])),
                    "table_content": len(lc_multimodal.get("table_content", [])),
                    "multimodal_chunks": len(lc_multimodal.get("multimodal_chunks", []))
                }

            # Add recommendation
            if comparison["both_enhanced"]:
                comparison["recommendation"] = "Both frameworks provided enhanced multimodal context"
            elif lc_enhanced:
                comparison["recommendation"] = "LangChain provided enhanced context, LlamaIndex used standard"
            elif li_enhanced:
                comparison["recommendation"] = "LlamaIndex provided enhanced context, LangChain used standard"
            else:
                comparison["recommendation"] = "Both frameworks used standard context"

            return comparison

        except Exception as e:
            logger.error(f"Error comparing multimodal contexts: {e}")
            return {"error": str(e)}
    
    def _query_with_langchain(
        self,
        question: str,
        category: str,
        k: Optional[int],
        enable_enhanced_context: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Query using LangChain components with enhanced multimodal context."""
        try:
            if not self.langchain_retriever:
                logger.warning("LangChain retriever not available")
                return {"error": "LangChain retriever not initialized"}

            # Check if enhanced retriever is available
            use_enhanced_retriever = False
            try:
                from app.services.langchain_integration.enhanced_adaptive_retriever import get_enhanced_adaptive_retriever

                if enable_enhanced_context:
                    # Create enhanced retriever
                    enhanced_retriever = get_enhanced_adaptive_retriever(
                        vectorstore=self.langchain_vectorstore,
                        base_k=k or 12,
                        category=category,
                        enable_enhanced_context=True
                    )
                    use_enhanced_retriever = True
                    logger.info("Using enhanced LangChain retriever")
                else:
                    enhanced_retriever = self.langchain_retriever

            except ImportError:
                enhanced_retriever = self.langchain_retriever
                logger.info("Enhanced retriever not available, using standard retriever")

            # Update retriever configuration
            if hasattr(enhanced_retriever, 'update_config'):
                if k is not None:
                    enhanced_retriever.update_config(base_k=k)
                if category:
                    enhanced_retriever.update_config(category=category)

            # Retrieve relevant documents
            documents = enhanced_retriever.get_relevant_documents(question)

            # Process results based on retriever type
            if use_enhanced_retriever:
                return self._process_enhanced_langchain_results(documents, enhanced_retriever, question)
            else:
                return self._process_standard_langchain_results(documents, enhanced_retriever)

        except Exception as e:
            logger.error(f"Error in LangChain query: {e}")
            return {"error": str(e)}

    def _process_enhanced_langchain_results(self, documents, retriever, question: str) -> Dict[str, Any]:
        """Process results from enhanced LangChain retriever."""
        try:
            # Find the enhanced context document
            enhanced_context_doc = None
            regular_documents = []
            multimodal_content = {
                "text_chunks": [],
                "image_content": [],
                "table_content": [],
                "multimodal_chunks": []
            }

            for doc in documents:
                content_type = doc.metadata.get("content_type", "text")

                if content_type == "enhanced_multimodal_context":
                    enhanced_context_doc = doc
                elif content_type == "enhanced_text_chunk":
                    multimodal_content["text_chunks"].append({
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    })
                elif content_type == "enhanced_image_content":
                    multimodal_content["image_content"].append({
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    })
                elif content_type == "enhanced_table_content":
                    multimodal_content["table_content"].append({
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    })
                elif content_type == "enhanced_multimodal_chunk":
                    multimodal_content["multimodal_chunks"].append({
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    })
                else:
                    regular_documents.append(doc)

            # Create comprehensive result
            result = {
                "framework": "langchain_enhanced",
                "documents_retrieved": len(documents),
                "enhanced_context_available": enhanced_context_doc is not None,
                "multimodal_content": multimodal_content,
                "regular_documents": [
                    {
                        "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                        "metadata": doc.metadata,
                        "source": doc.metadata.get("source", "unknown")
                    }
                    for doc in regular_documents[:5]
                ],
                "retriever_info": retriever.get_enhanced_retriever_info() if hasattr(retriever, 'get_enhanced_retriever_info') else {}
            }

            # Add enhanced context information
            if enhanced_context_doc:
                result["enhanced_context"] = {
                    "query_type": enhanced_context_doc.metadata.get("query_type"),
                    "query_confidence": enhanced_context_doc.metadata.get("query_confidence"),
                    "total_images": enhanced_context_doc.metadata.get("total_images", 0),
                    "total_tables": enhanced_context_doc.metadata.get("total_tables", 0),
                    "total_multimodal_chunks": enhanced_context_doc.metadata.get("total_multimodal_chunks", 0),
                    "context_richness_score": enhanced_context_doc.metadata.get("context_richness_score", 0),
                    "has_spatial_relationships": enhanced_context_doc.metadata.get("has_spatial_relationships", False)
                }

                # Include formatted context for LLM
                result["formatted_context"] = enhanced_context_doc.page_content

            return result

        except Exception as e:
            logger.error(f"Error processing enhanced LangChain results: {e}")
            return self._process_standard_langchain_results(documents, retriever)

    def _process_standard_langchain_results(self, documents, retriever) -> Dict[str, Any]:
        """Process results from standard LangChain retriever."""
        return {
            "framework": "langchain",
            "documents_retrieved": len(documents),
            "documents": [
                {
                    "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    "metadata": doc.metadata,
                    "source": doc.metadata.get("source", "unknown")
                }
                for doc in documents
            ],
            "retriever_info": retriever.get_retriever_info() if hasattr(retriever, 'get_retriever_info') else {}
        }
    
    def _query_with_llamaindex(
        self,
        question: str,
        category: str,
        k: Optional[int],
        enable_enhanced_context: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Query using LlamaIndex components with enhanced multimodal context."""
        try:
            if not self.llamaindex_rag_service:
                logger.warning("LlamaIndex service not available")
                return {"error": "LlamaIndex service not initialized"}

            # Check if enhanced multimodal query is available
            use_enhanced_query = False
            try:
                if enable_enhanced_context and hasattr(self.llamaindex_rag_service, 'answer_question_with_enhanced_context'):
                    # Use enhanced multimodal query
                    result = self.llamaindex_rag_service.answer_question_with_enhanced_context(
                        question=question,
                        category=category,
                        k=k,
                        include_spatial_relationships=True,
                        include_cross_references=True,
                        max_context_length=6000
                    )
                    use_enhanced_query = True
                    logger.info("Using enhanced LlamaIndex multimodal query")
                else:
                    # Use standard multimodal query
                    result = self.llamaindex_rag_service.answer_question(
                        question=question,
                        category=category,
                        enable_multimodal=True,
                        k=k,
                        **kwargs
                    )

            except Exception as e:
                logger.warning(f"Enhanced LlamaIndex query failed, falling back to standard: {e}")
                result = self.llamaindex_rag_service.answer_question(
                    question=question,
                    category=category,
                    enable_multimodal=True,
                    k=k,
                    **kwargs
                )

            # Process and enhance the result
            if use_enhanced_query:
                return self._process_enhanced_llamaindex_results(result, question)
            else:
                return self._process_standard_llamaindex_results(result)

        except Exception as e:
            logger.error(f"Error in LlamaIndex query: {e}")
            return {"error": str(e)}

    def _process_enhanced_llamaindex_results(self, result: Dict[str, Any], question: str) -> Dict[str, Any]:
        """Process results from enhanced LlamaIndex query."""
        try:
            enhanced_result = {
                "framework": "llamaindex_enhanced",
                "answer": result.get("answer", ""),
                "question": question,
                "search_mode": result.get("search_mode", "enhanced_multimodal"),
                "retrieval_k": result.get("retrieval_k", 12)
            }

            # Add query analysis information
            query_analysis = result.get("query_analysis", {})
            if query_analysis:
                enhanced_result["query_analysis"] = query_analysis

            # Add context metadata
            context_metadata = result.get("context_metadata", {})
            if context_metadata:
                enhanced_result["context_metadata"] = context_metadata
                enhanced_result["multimodal_content_summary"] = {
                    "total_text_chunks": context_metadata.get("total_text_chunks", 0),
                    "total_images": context_metadata.get("total_images", 0),
                    "total_tables": context_metadata.get("total_tables", 0),
                    "total_multimodal_chunks": context_metadata.get("total_multimodal_chunks", 0),
                    "context_richness_score": context_metadata.get("context_richness_score", 0),
                    "has_spatial_relationships": context_metadata.get("has_spatial_relationships", False)
                }

            # Add enhanced context information if available
            enhanced_context = result.get("enhanced_context")
            if enhanced_context:
                enhanced_result["enhanced_context_available"] = True
                enhanced_result["spatial_relationships_included"] = result.get("spatial_relationships_included", False)
                enhanced_result["cross_references_included"] = result.get("cross_references_included", False)

                # Add formatted context for comparison with LangChain
                if hasattr(enhanced_context, 'formatted_context'):
                    enhanced_result["formatted_context"] = enhanced_context.formatted_context[:500] + "..."
            else:
                enhanced_result["enhanced_context_available"] = False

            # Add search results summary
            search_results = result.get("search_results", {})
            if search_results:
                enhanced_result["search_results_summary"] = {
                    "total_results": len(search_results.get("merged_results", [])),
                    "search_mode": search_results.get("search_mode", "unknown")
                }

            return enhanced_result

        except Exception as e:
            logger.error(f"Error processing enhanced LlamaIndex results: {e}")
            return self._process_standard_llamaindex_results(result)

    def _process_standard_llamaindex_results(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process results from standard LlamaIndex query."""
        return {
            "framework": "llamaindex",
            "answer_result": result,
            "answer": result.get("answer", ""),
            "search_mode": result.get("search_mode", "standard"),
            "enhanced_context_used": result.get("enhanced_context_used", False)
        }
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get information about the unified service."""
        return {
            "service_type": "unified_rag",
            "default_framework": self.default_framework.value,
            "category": self.category,
            "langchain_available": all([
                self.langchain_splitter,
                self.langchain_vectorstore,
                self.langchain_retriever
            ]),
            "llamaindex_available": all([
                self.llamaindex_rag_service,
                self.llamaindex_chunking_service
            ]),
            "shared_components": {
                "document_processor": self.document_processor is not None,
                "embeddings": self.embeddings is not None
            }
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics from both frameworks."""
        stats = {}
        
        try:
            # LangChain cache stats
            if self.embeddings:
                stats["langchain_embeddings"] = self.embeddings.get_cache_stats()
            
            # LlamaIndex cache stats (if available)
            if self.llamaindex_rag_service:
                # Add LlamaIndex cache stats if available
                pass
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}


# Global service instance
_unified_rag_service = None

def get_unified_rag_service(
    default_framework: RAGFramework = RAGFramework.BOTH,
    category: Optional[str] = None
) -> UnifiedRAGService:
    """
    Get the global unified RAG service instance.
    
    Args:
        default_framework: Default framework to use
        category: Default category for operations
        
    Returns:
        UnifiedRAGService instance
    """
    global _unified_rag_service
    if _unified_rag_service is None:
        _unified_rag_service = UnifiedRAGService(
            default_framework=default_framework,
            category=category
        )
    return _unified_rag_service
